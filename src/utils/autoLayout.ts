
/**
 * Enhanced horizontal multi-row flexible auto-layout for nodes.
 * - Nodes are placed in a horizontal grid with increased spacing.
 * - Supports multidirectional connections with appropriate spacing.
 * - Extra padding added on all sides.
 * - Second row flows right to left.
 * - Final output node is placed at the left-most position of third row.
 */
import { nodeDefaultStyle } from '@/pages/IndustryFlow.constants';

// Type definitions
interface Node {
  id: string;
  position: { x: number; y: number };
  style?: Record<string, unknown>;
  type?: string;
  data?: Record<string, unknown>;
  [key: string]: unknown;
}

interface Edge {
  id: string;
  source: string;
  target: string;
  [key: string]: unknown;
}

interface LayoutOptions {
  nodeWidth?: number;
  nodeHeight?: number;
  levelGap?: number;
  nodeGap?: number;
  baseX?: number;
  baseY?: number;
  padding?: number;
  gapX?: number;
  gapY?: number;
}

// Example node groups for horizontal layout (update as needed)
const topRow = ['2', '3', '4', '5'];
const botRow = ['6', '7', '8', '9'];
const finalRow = ['natural-gas']; // Define the final output row

export function horizontalGridAutoLayout(nodes: Node[], gapX = 350, gapY = 250, baseX = 220, baseY = 200) {
  const nodeMap = Object.fromEntries(nodes.map(n => [n.id, n]));
  const result: Node[] = [];

  // Place input dots, if present
  const inputs = nodes.filter(n => n.type === 'inputdot');
  if (inputs.length === 2) {
    result.push({
      ...inputs[0],
      position: { 
        // Place the first input dot on the left side for top row
        x: baseX - gapX * 0.6, 
        y: baseY 
      },
      style: { ...inputs[0].style },
      type: inputs[0].type,
    });
    result.push({
      ...inputs[1],
      position: { 
        // Place the second input dot on the right side for bottom row
        x: baseX + gapX * (botRow.length - 1) + gapX * 0.6, 
        y: baseY + gapY * 1 
      },
      style: { ...inputs[1].style },
      type: inputs[1].type,
    });
  }

  // Top row with increased spacing (left to right)
  topRow.forEach((id, idx) => {
    const node = nodeMap[id];
    if (node) {
      result.push({
        ...node,
        position: { x: baseX + gapX * idx, y: baseY },
        style: { ...nodeDefaultStyle, ...node.style },
        type: 'custom',
      });
    }
  });

  // Bottom row with increased spacing (RIGHT TO LEFT)
  botRow.forEach((id, idx) => {
    const node = nodeMap[id];
    if (node) {
      // Calculate position from right to left
      // Start from the rightmost position and move left
      const rightToLeftIdx = botRow.length - 1 - idx;
      
      result.push({
        ...node,
        position: { x: baseX + gapX * rightToLeftIdx, y: baseY + gapY },
        style: { ...nodeDefaultStyle, ...node.style },
        type: 'custom',
      });
    }
  });
  
  // Final output node in the third row (left-most position)
  finalRow.forEach((id) => {
    const node = nodeMap[id];
    if (node) {
      result.push({
        ...node,
        position: { x: baseX, y: baseY + gapY * 2 },
        style: { ...nodeDefaultStyle, ...node.style },
        type: node.type, // Keep the original node type (finalOutput)
      });
    }
  });

  // Fallback for scratch/single row layouts with improved spacing
  if (result.length === 0) {
    return nodes.map((node, idx) => ({
      ...node,
      position: { x: baseX + gapX * (idx % 3), y: baseY + gapY * Math.floor(idx / 3) },
      style: { ...nodeDefaultStyle, ...node.style },
      type: 'custom',
    }));
  }

  return result;
}

/**
 * Hierarchical auto layout that arranges nodes in a tree-like structure
 * based on their connections and dependencies.
 */
export function hierarchicalAutoLayout(nodes: Node[], edges: Edge[], options: LayoutOptions = {}) {
  const {
    nodeWidth = 180,
    nodeHeight = 60,
    levelGap = 300,  // Horizontal gap between levels
    nodeGap = 200,   // Vertical gap between nodes in same level
    baseX = 100,
    baseY = 100,
    padding = 50
  } = options;

  if (nodes.length === 0) return nodes;

  // Create adjacency lists for both directions
  const outgoing = new Map<string, string[]>();
  const incoming = new Map<string, string[]>();
  const nodeMap = new Map(nodes.map(node => [node.id, node]));

  // Initialize adjacency lists
  nodes.forEach(node => {
    outgoing.set(node.id, []);
    incoming.set(node.id, []);
  });

  // Build adjacency lists from edges
  edges.forEach(edge => {
    if (outgoing.has(edge.source) && incoming.has(edge.target)) {
      outgoing.get(edge.source)!.push(edge.target);
      incoming.get(edge.target)!.push(edge.source);
    }
  });

  // Find root nodes (nodes with no incoming edges)
  const rootNodes = nodes.filter(node => incoming.get(node.id)!.length === 0);
  
  // If no root nodes found, use nodes with the least incoming edges
  if (rootNodes.length === 0) {
    const minIncoming = Math.min(...Array.from(incoming.values()).map(edges => edges.length));
    const potentialRoots = nodes.filter(node => incoming.get(node.id)!.length === minIncoming);
    rootNodes.push(...potentialRoots);
  }

  // Perform topological sort to determine levels
  const levels: string[][] = [];
  const visited = new Set<string>();
  const inDegree = new Map<string, number>();

  // Initialize in-degree count
  nodes.forEach(node => {
    inDegree.set(node.id, incoming.get(node.id)!.length);
  });

  // Start with root nodes
  let currentLevel = rootNodes.map(node => node.id);
  levels.push(currentLevel);

  while (currentLevel.length > 0) {
    const nextLevel: string[] = [];
    
    for (const nodeId of currentLevel) {
      visited.add(nodeId);
      
      // Add children to next level
      for (const childId of outgoing.get(nodeId)!) {
        if (!visited.has(childId)) {
          inDegree.set(childId, inDegree.get(childId)! - 1);
          if (inDegree.get(childId) === 0) {
            nextLevel.push(childId);
          }
        }
      }
    }
    
    if (nextLevel.length > 0) {
      levels.push(nextLevel);
    }
    currentLevel = nextLevel;
  }

  // Add any remaining nodes (cycles or disconnected components)
  const remainingNodes = nodes.filter(node => !visited.has(node.id));
  if (remainingNodes.length > 0) {
    levels.push(remainingNodes.map(node => node.id));
  }

  // Position nodes level by level
  const positionedNodes: Node[] = [];
  
  levels.forEach((levelNodeIds, levelIndex) => {
    const levelX = baseX + levelIndex * levelGap;
    
    levelNodeIds.forEach((nodeId, nodeIndex) => {
      const node = nodeMap.get(nodeId);
      if (node) {
        // Center nodes in the level
        const totalHeight = (levelNodeIds.length - 1) * nodeGap;
        const startY = baseY - totalHeight / 2;
        const nodeY = startY + nodeIndex * nodeGap;
        
        positionedNodes.push({
          ...node,
          position: { x: levelX, y: nodeY },
          style: { ...nodeDefaultStyle, ...node.style },
          type: node.type || 'custom',
        });
      }
    });
  });

  return positionedNodes;
}

/**
 * Smart auto layout that chooses between hierarchical and grid layout
 * based on the flow structure.
 */
export function smartAutoLayout(nodes: Node[], edges: Edge[], options: LayoutOptions = {}) {
  if (nodes.length === 0) return nodes;

  // Count connections to determine layout type
  const connectionCount = edges.length;
  const nodeCount = nodes.length;
  const avgConnections = connectionCount / nodeCount;

  // If there are many connections relative to nodes, use hierarchical
  if (avgConnections > 0.8 && connectionCount > 3) {
    return hierarchicalAutoLayout(nodes, edges, options);
  } else {
    // Use grid layout for simpler flows
    return horizontalGridAutoLayout(nodes, options.gapX || 350, options.gapY || 250, options.baseX || 220, options.baseY || 200);
  }
}
