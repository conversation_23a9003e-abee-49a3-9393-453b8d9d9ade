import { filterValidNodes, getNodeLabel } from "@/utils/dropdownUtils";
import React, { useState, useEffect } from "react";
import { fetchActivityNameTechnologies } from '@/services/connectionFormApi';
import { useToastContext } from '@/contexts/ToastContext';
import { AddResourceDropdownOption } from './AddResourceDropdownOption';
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Plus, Trash } from "lucide-react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  FormData,
  byproducts,
  units,
  emissions,
  EnergyByProduct,
  MaterialByProduct,
  EmissionByProduct,
  techs,
  TechnologyResponse,
  MaterialResponse,
  EnergyResponse,
  EmissionResponse
} from "./types";

interface ByProductsStepProps {
  formData: FormData;
  updateFormField: (section: string, field: string, value: string) => void;
  updateField: (field: string, value: string) => void;
  errors: Record<string, string>;
  availableNodes?: any[];
  technologyAutoFillLabel?: string;
  availableTechnologies?: string[];
  technologies: string[];
  activeTechnology: string;
  setActiveTechnology: (tech: string) => void;
  onAddTechnology?: () => void;
  readOnly?: boolean;
  // API data props
  apiTechnologies?: TechnologyResponse[];
  apiMaterials?: MaterialResponse[];
  apiEnergies?: EnergyResponse[];
  apiEmissions?: EmissionResponse[];
  isLoadingApiData?: boolean;
  sectorUuid?: string;
  onMaterialAdded?: (material: MaterialResponse) => void;
  onEnergyAdded?: (energy: EnergyResponse) => void;
}

export const ByProductsStep: React.FC<ByProductsStepProps> = ({
  formData,
  updateFormField,
  updateField,
  errors,
  availableNodes = [],
  technologyAutoFillLabel,
  availableTechnologies = [],
  technologies,
  activeTechnology,
  setActiveTechnology,
  onAddTechnology,
  readOnly = false,
  apiTechnologies = [],
  apiMaterials = [],
  apiEnergies = [],
  apiEmissions = [],
  isLoadingApiData = false,
  sectorUuid,
  onMaterialAdded,
  onEnergyAdded
}) => {
  const { toast } = useToastContext();

  // State for managing add resource dialogs
  const [showMaterialDialog, setShowMaterialDialog] = useState(false);
  const [showEnergyDialog, setShowEnergyDialog] = useState(false);
  const [currentMaterialByProductIndex, setCurrentMaterialByProductIndex] = useState<number | null>(null);
  const [currentEnergyByProductIndex, setCurrentEnergyByProductIndex] = useState<number | null>(null);

  // State to store technologies for each destination activity
  const [destinationActivityTechnologies, setDestinationActivityTechnologies] = useState<Record<string, TechnologyResponse[]>>({});
  const [loadingDestinationTechnologies, setLoadingDestinationTechnologies] = useState<Record<string, boolean>>({});

  // Get data from API or fallback to hardcoded arrays
  const availableTechnologiesData = apiTechnologies.length > 0
    ? apiTechnologies.map(tech => tech.title || tech.name).filter(name => name && name.trim() !== "")
    : techs.filter(tech => tech && tech.trim() !== "");

  const availableMaterialsData = apiMaterials.length > 0
    ? apiMaterials.map(material => material.name).filter(name => name && name.trim() !== "")
    : byproducts.filter(material => material && material.trim() !== "");

  const availableEnergiesData = apiEnergies.length > 0
    ? apiEnergies.map(energy => energy.name).filter(name => name && name.trim() !== "")
    : byproducts.filter(energy => energy && energy.trim() !== "");

  const availableEmissionsData = apiEmissions.length > 0
    ? apiEmissions.map(emission => emission.name).filter(name => name && name.trim() !== "")
    : emissions.filter(emission => emission && emission.trim() !== "");

  // Initialize the state with the current technology or default to first available
  const [selectedTechnology, setSelectedTechnology] = useState<string>(
    formData.byproductTechnology || availableTechnologies[0] || availableTechnologiesData[0] || ""
  );

  const [energyByProducts, setEnergyByProducts] = useState<EnergyByProduct[]>(
    formData.byproductEnergy && Array.isArray(formData.byproductEnergy) 
      ? formData.byproductEnergy 
      : formData.byproductEnergy 
        ? [formData.byproductEnergy]
        : [{
            byproduct: "",
            unit: "GJ",
            bppo: "",
            connect: "",
            replaced: "",
            emissions: [], // Initialize with empty emissions array
          }]
  );

  const [materialByProducts, setMaterialByProducts] = useState<MaterialByProduct[]>(
    formData.byproductMat && Array.isArray(formData.byproductMat)
      ? formData.byproductMat
      : formData.byproductMat
        ? [formData.byproductMat]
        : [{
            byproduct: "",
            unit: "Tonnes",
            bppo: "",
            connect: "",
            replaced: "",
            techEmissionFactor: "",
            emissionFactor: "",
            emissionUnit: "",
          }]
  );

  // Helper function to convert node ID to human-readable activity name
  const getActivityNameFromId = (nodeId: string): string => {
    if (!nodeId || nodeId === "Nil") return "Nil";

    // Find the node in availableNodes
    const node = availableNodes.find(n => n.id === nodeId);
    if (node) {
      return getNodeLabel(node);
    }

    // If not found, return the ID as fallback (this handles the case where it's already a name)
    return nodeId;
  };

  // Function to fetch technologies for a specific destination activity
  const fetchTechnologiesForDestinationActivity = async (destinationActivityNodeId: string) => {
    if (!destinationActivityNodeId || destinationActivityNodeId === 'Nil') {
      return [];
    }

    // Convert node ID to activity name
    const activityName = getActivityNameFromId(destinationActivityNodeId);
    if (!activityName || activityName === 'Nil') {
      return [];
    }

    // Check if we already have the technologies cached
    if (destinationActivityTechnologies[destinationActivityNodeId]) {
      return destinationActivityTechnologies[destinationActivityNodeId];
    }

    // Check if we're already loading this activity's technologies
    if (loadingDestinationTechnologies[destinationActivityNodeId]) {
      return [];
    }

    try {
      setLoadingDestinationTechnologies(prev => ({ ...prev, [destinationActivityNodeId]: true }));
      // Use the activity name for the API call, now with sectorUuid for UUID-based fetching
      const technologies = await fetchActivityNameTechnologies(activityName, { toast }, sectorUuid);

      setDestinationActivityTechnologies(prev => ({
        ...prev,
        [destinationActivityNodeId]: technologies
      }));

      return technologies;
    } catch (error) {
      console.error(`Error fetching technologies for destination activity ${activityName} (node: ${destinationActivityNodeId}):`, error);
      return [];
    } finally {
      setLoadingDestinationTechnologies(prev => ({ ...prev, [destinationActivityNodeId]: false }));
    }
  };

  // Helper function to get technologies for a specific destination activity
  const getTechnologiesForDestinationActivity = (destinationActivityNodeId: string): string[] => {
    if (!destinationActivityNodeId || destinationActivityNodeId === 'Nil') {
      return ['Nil'];
    }

    // Get technologies from cache
    const cachedTechnologies = destinationActivityTechnologies[destinationActivityNodeId];
    if (cachedTechnologies && cachedTechnologies.length > 0) {
      return cachedTechnologies.map(tech => tech.title || tech.name).filter(name => name && name.trim() !== "");
    }

    // If no cached technologies and not loading, return fallback message
    if (!loadingDestinationTechnologies[destinationActivityNodeId]) {
      return ['No technologies available'];
    }

    // If loading or no data yet, return empty array
    return [];
  };

  // Filter out invalid nodes including "input1"
  const validNodes = filterValidNodes(availableNodes);

  // Pre-fetch technologies for existing destination activities
  useEffect(() => {
    const destinationActivities = new Set<string>();

    // Collect all destination activities from energy byproducts
    energyByProducts.forEach(energyByProduct => {
      if (energyByProduct.connect && energyByProduct.connect !== 'Nil') {
        destinationActivities.add(energyByProduct.connect);
      }
    });

    // Collect all destination activities from material byproducts
    materialByProducts.forEach(materialByProduct => {
      if (materialByProduct.connect && materialByProduct.connect !== 'Nil') {
        destinationActivities.add(materialByProduct.connect);
      }
    });

    // Fetch technologies for each unique destination activity
    destinationActivities.forEach(destinationActivity => {
      fetchTechnologiesForDestinationActivity(destinationActivity);
    });
  }, [energyByProducts.map(bp => bp.connect).join(','), materialByProducts.map(bp => bp.connect).join(',')]);

  // Effect to update the form data with the byproduct technology when it changes
  useEffect(() => {
    // Set the default technology if none is selected yet
    if (selectedTechnology) {
      updateField('byproductTechnology', selectedTechnology);
    } else if (availableTechnologies.length > 0) {
      const defaultTech = availableTechnologies[0];
      setSelectedTechnology(defaultTech);
      updateField('byproductTechnology', defaultTech);
    }
  }, [selectedTechnology, availableTechnologies, updateField]);

  // Effect to update state when formData changes (for restored data) - only run once on mount
  useEffect(() => {
    if (formData?.byproductEnergy && Array.isArray(formData.byproductEnergy)) {
      setEnergyByProducts(formData.byproductEnergy);
    } else if (formData?.byproductEnergy && !Array.isArray(formData.byproductEnergy)) {
      setEnergyByProducts([formData.byproductEnergy]);
    }
    
    if (formData?.byproductMat && Array.isArray(formData.byproductMat)) {
      setMaterialByProducts(formData.byproductMat);
    } else if (formData?.byproductMat && !Array.isArray(formData.byproductMat)) {
      setMaterialByProducts([formData.byproductMat]);
    }
  }, []); // Empty dependency array - only run once on mount

  // Handle technology change for the entire by-products section
  const handleTechnologyChange = (value: string) => {
    setSelectedTechnology(value);
    updateField('byproductTechnology', value);
  };

  const addEnergyByProduct = () => {
    const newEnergyByProduct: EnergyByProduct = {
      byproduct: "",
      unit: "GJ",
      bppo: "",
      connect: "",
      replaced: "",
      emissions: [], // Initialize with empty emissions array
    };
    setEnergyByProducts([...energyByProducts, newEnergyByProduct]);
  };

  const addMaterialByProduct = () => {
    const newMaterialByProduct: MaterialByProduct = {
      byproduct: "",
      unit: "Tonnes",
      bppo: "",
      connect: "",
      replaced: "",
      techEmissionFactor: "",
      emissionFactor: "",
      emissionUnit: "",
    };
    setMaterialByProducts([...materialByProducts, newMaterialByProduct]);
  };

  // Fix the type error by using a generic type parameter and type guard
  const updateEnergyByProduct = <K extends keyof EnergyByProduct>(
    index: number,
    field: K,
    value: EnergyByProduct[K]
  ) => {
    const updatedEnergyByProducts = [...energyByProducts];
    // Handle special case for emissions array to avoid type issues
    if (field === 'emissions' as K) {
      updatedEnergyByProducts[index][field] = value;
    } else {
      // For string fields, assign directly
      updatedEnergyByProducts[index][field] = value;
    }
    setEnergyByProducts(updatedEnergyByProducts);
    // Update formData to reflect changes
    if (index === 0) {
      updateFormField('byproductEnergy', field as string, value as string);
    }

    // If destination activity changed, fetch technologies for the new destination activity
    if (field === 'connect' && value && value !== 'Nil') {
      fetchTechnologiesForDestinationActivity(value as string);
    }
  };

  const updateMaterialByProduct = <K extends keyof MaterialByProduct>(
    index: number,
    field: K,
    value: MaterialByProduct[K]
  ) => {
    const updatedMaterialByProducts = [...materialByProducts];
    updatedMaterialByProducts[index][field] = value;
    setMaterialByProducts(updatedMaterialByProducts);
    // Update formData to reflect changes
    if (index === 0) {
      updateFormField('byproductMat', field as string, value as string);
    }

    // If destination activity changed, fetch technologies for the new destination activity
    if (field === 'connect' && value && value !== 'Nil') {
      fetchTechnologiesForDestinationActivity(value as string);
    }
  };

  const removeEnergyByProduct = (index: number) => {
    const updatedEnergyByProducts = [...energyByProducts];
    updatedEnergyByProducts.splice(index, 1);
    setEnergyByProducts(updatedEnergyByProducts);
  };

  const removeMaterialByProduct = (index: number) => {
    const updatedMaterialByProducts = [...materialByProducts];
    updatedMaterialByProducts.splice(index, 1);
    setMaterialByProducts(updatedMaterialByProducts);
  };

  // New methods for emissions handling within energy by-products
  const addEmissionToEnergyByProduct = (energyByProductIndex: number) => {
    const updatedEnergyByProducts = [...energyByProducts];
    const newEmission: EmissionByProduct = {
      id: `emission-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      name: "",
      factor: "",
      unit: "kg"
    };
    
    // Ensure the emissions array exists
    if (!updatedEnergyByProducts[energyByProductIndex].emissions) {
      updatedEnergyByProducts[energyByProductIndex].emissions = [];
    }
    
    // Add the new emission to the array
    updatedEnergyByProducts[energyByProductIndex].emissions!.push(newEmission);
    setEnergyByProducts(updatedEnergyByProducts);
  };

  const updateEmissionInEnergyByProduct = (
    energyByProductIndex: number,
    emissionIndex: number,
    field: keyof EmissionByProduct,
    value: string
  ) => {
    const updatedEnergyByProducts = [...energyByProducts];
    
    if (updatedEnergyByProducts[energyByProductIndex].emissions) {
      updatedEnergyByProducts[energyByProductIndex].emissions![emissionIndex][field] = value;
      setEnergyByProducts(updatedEnergyByProducts);
    }
  };

  const removeEmissionFromEnergyByProduct = (energyByProductIndex: number, emissionIndex: number) => {
    const updatedEnergyByProducts = [...energyByProducts];
    
    if (updatedEnergyByProducts[energyByProductIndex].emissions) {
      updatedEnergyByProducts[energyByProductIndex].emissions!.splice(emissionIndex, 1);
      setEnergyByProducts(updatedEnergyByProducts);
    }
  };

  // Save complete by-product data to formData
  useEffect(() => {
    updateField('energyByProducts', JSON.stringify(energyByProducts));
    updateField('materialByProducts', JSON.stringify(materialByProducts));
  }, [energyByProducts, materialByProducts, updateField]);

  return (
    <div className={readOnly ? "opacity-75" : ""}>
      {/* Technology tabs are now handled at the dialog level */}
      {renderTechnologyContent(activeTechnology)}
    </div>
  );

  function renderTechnologyContent(technology: string) {
    return (
      <>

      {/* Energy By-product Section */}
      <div className="border-2 border-green-300 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Energy By-product</div>
          {!readOnly && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addEnergyByProduct}
              className="h-6 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Energy By-product
            </Button>
          )}
        </div>
        {energyByProducts.map((energyByProduct, index) => (
          <div key={index} className="mb-4 p-3 border rounded-md">
            <div className="flex items-center justify-between mb-2">
              <div className="font-semibold text-sm">By-product {index + 1}</div>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={() => removeEnergyByProduct(index)}
                className="h-6 px-2 text-xs"
              >
                <Trash className="h-3 w-3 mr-1" />
                Remove
              </Button>
            </div>
            
            <div className="flex flex-col md:flex-row gap-2">
              <div className="flex-1 min-w-0">
                <label>Name</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {energyByProduct.byproduct || (isLoadingApiData ? "Loading energies..." : "Select")}
                  </div>
                ) : (
                  <>
                    <select
                      className="w-full border rounded px-2 py-2"
                      value={energyByProduct.byproduct}
                      onChange={(e) => {
                        if (e.target.value === "__ADD_NEW__") {
                          // Reset the select value and open the dialog
                          e.target.value = energyByProduct.byproduct;
                          setShowEnergyDialog(true);
                          setCurrentEnergyByProductIndex(index);
                          return;
                        }
                        updateEnergyByProduct(index, 'byproduct', e.target.value as string);
                      }}
                      disabled={isLoadingApiData || readOnly}
                    >
                      <option value="">{isLoadingApiData ? "Loading energies..." : "Select"}</option>
                      {availableEnergiesData.map(v => <option key={v} value={v}>{v}</option>)}
                      {!readOnly && (
                        <option value="__ADD_NEW__" style={{ fontWeight: 'bold', color: '#2563eb' }}>
                          + Add New Energy
                        </option>
                      )}
                    </select>
                    {showEnergyDialog && (
                      <AddResourceDropdownOption
                        type="energy"
                        onResourceAdded={(energy) => {
                          if (onEnergyAdded) {
                            onEnergyAdded(energy as EnergyResponse);
                          }
                          // Set the newly created energy as selected
                          if (currentEnergyByProductIndex !== null) {
                            updateEnergyByProduct(currentEnergyByProductIndex, 'byproduct', energy.name);
                          }
                          setShowEnergyDialog(false);
                          setCurrentEnergyByProductIndex(null);
                        }}
                        disabled={isLoadingApiData}
                      />
                    )}
                  </>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>Unit</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {energyByProduct.unit}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2"
                    value={energyByProduct.unit}
                    onChange={(e) => updateEnergyByProduct(index, 'unit', e.target.value as string)}
                  >
                    {units.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>BP/PO</label>
                <Input 
                  type="text"
                  value={energyByProduct.bppo}
                  onChange={(e) => updateEnergyByProduct(index, 'bppo', e.target.value as string)}
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-2 mt-2">
              <div className="flex-1 min-w-0">
                <label>Destination activity</label>
                {readOnly ? (
                  <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {energyByProduct.connect || "Select activity"}
                  </div>
                ) : (
                  <Select
                    value={energyByProduct.connect || undefined}
                    onValueChange={(value) => updateEnergyByProduct(index, 'connect', value as string)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select activity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">No connection (standalone)</SelectItem>
                      {validNodes.map(node => (
                        <SelectItem key={node.id} value={node.id}>{node.data?.label || node.id}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>Select technology</label>
                {readOnly ? (
                  <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {energyByProduct.technology || "Select technology"}
                  </div>
                ) : (
                  <Select
                    value={energyByProduct.technology || ""}
                    onValueChange={(value) => updateEnergyByProduct(index, 'technology', value as string)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select technology" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingDestinationTechnologies[energyByProduct.connect] ? (
                        <SelectItem value="loading" disabled>Loading technologies...</SelectItem>
                      ) : (() => {
                        const technologies = getTechnologiesForDestinationActivity(energyByProduct.connect);

                        if (technologies.length === 0) {
                          return <SelectItem value="no-tech" disabled>No technologies available</SelectItem>;
                        }

                        return technologies.map(tech => (
                          <SelectItem
                            key={tech}
                            value={tech}
                            disabled={tech === "No technologies available"}
                          >
                            {tech}
                          </SelectItem>
                        ));
                      })()}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-2 mt-2">
              <div className="w-full md:w-1/2">
                <label>Energy replaced</label>
                <Input 
                  type="text"
                  value={energyByProduct.replaced}
                  onChange={(e) => updateEnergyByProduct(index, 'replaced', e.target.value as string)}
                />
              </div>
            </div>


          </div>
        ))}
      </div>

      {/* Material By-product Section */}
      <div className="border-2 border-purple-300 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Material By-product</div>
          {!readOnly && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addMaterialByProduct}
              className="h-6 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Material By-product
            </Button>
          )}
        </div>
        {materialByProducts.map((materialByProduct, index) => (
          <div key={index} className="mb-4 p-3 border rounded-md">
            <div className="flex items-center justify-between mb-2">
              <div className="font-semibold text-sm">By-product {index + 1}</div>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                onClick={() => removeMaterialByProduct(index)}
                className="h-6 px-2 text-xs"
              >
                <Trash className="h-3 w-3 mr-1" />
                Remove
              </Button>
            </div>
            
            <div className="flex flex-col md:flex-row gap-2">
              <div className="flex-1 min-w-0">
                <label>Name</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {materialByProduct.byproduct || (isLoadingApiData ? "Loading materials..." : "Select")}
                  </div>
                ) : (
                  <>
                    <select
                      className="w-full border rounded px-2 py-2"
                      value={materialByProduct.byproduct}
                      onChange={(e) => {
                        if (e.target.value === "__ADD_NEW__") {
                          // Reset the select value and open the dialog
                          e.target.value = materialByProduct.byproduct;
                          setShowMaterialDialog(true);
                          setCurrentMaterialByProductIndex(index);
                          return;
                        }
                        updateMaterialByProduct(index, 'byproduct', e.target.value as string);
                      }}
                      disabled={isLoadingApiData || readOnly}
                    >
                      <option value="">{isLoadingApiData ? "Loading materials..." : "Select"}</option>
                      {availableMaterialsData.map(v => <option key={v} value={v}>{v}</option>)}
                      {!readOnly && sectorUuid && (
                        <option value="__ADD_NEW__" style={{ fontWeight: 'bold', color: '#2563eb' }}>
                          + Add New Material
                        </option>
                      )}
                    </select>
                    {showMaterialDialog && (
                      <AddResourceDropdownOption
                        type="material"
                        sectorUuid={sectorUuid}
                        onResourceAdded={(material) => {
                          if (onMaterialAdded) {
                            onMaterialAdded(material as MaterialResponse);
                          }
                          // Set the newly created material as selected
                          if (currentMaterialByProductIndex !== null) {
                            updateMaterialByProduct(currentMaterialByProductIndex, 'byproduct', material.name);
                          }
                          setShowMaterialDialog(false);
                          setCurrentMaterialByProductIndex(null);
                        }}
                        disabled={isLoadingApiData}
                      />
                    )}
                  </>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>Unit</label>
                {readOnly ? (
                  <div className="w-full border rounded px-2 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {materialByProduct.unit}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2"
                    value={materialByProduct.unit}
                    onChange={(e) => updateMaterialByProduct(index, 'unit', e.target.value as string)}
                  >
                    {units.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>BP/PO</label>
                <Input 
                  type="text"
                  value={materialByProduct.bppo}
                  onChange={(e) => updateMaterialByProduct(index, 'bppo', e.target.value as string)}
                />
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-2 mt-2">
              <div className="flex-1 min-w-0">
                <label>Destination activity</label>
                {readOnly ? (
                  <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {materialByProduct.connect || "Select activity"}
                  </div>
                ) : (
                  <Select
                    value={materialByProduct.connect || undefined}
                    onValueChange={(value) => updateMaterialByProduct(index, 'connect', value as string)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select activity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">No connection (standalone)</SelectItem>
                      {validNodes.map(node => (
                        <SelectItem key={node.id} value={node.id}>{node.data?.label || node.id}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <label>Select technology</label>
                {readOnly ? (
                  <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                    {materialByProduct.technology || "Select technology"}
                  </div>
                ) : (
                  <Select
                    value={materialByProduct.technology || ""}
                    onValueChange={(value) => updateMaterialByProduct(index, 'technology', value as string)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select technology" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingDestinationTechnologies[materialByProduct.connect] ? (
                        <SelectItem value="loading" disabled>Loading technologies...</SelectItem>
                      ) : (() => {
                        const technologies = getTechnologiesForDestinationActivity(materialByProduct.connect);

                        if (technologies.length === 0) {
                          return <SelectItem value="no-tech" disabled>No technologies available</SelectItem>;
                        }

                        return technologies.map(tech => (
                          <SelectItem
                            key={tech}
                            value={tech}
                            disabled={tech === "No technologies available"}
                          >
                            {tech}
                          </SelectItem>
                        ));
                      })()}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
            <div className="flex flex-col md:flex-row gap-2 mt-2">
              <div className="w-full md:w-1/2">
                <label>Energy replaced</label>
                <Input 
                  type="text"
                  value={materialByProduct.replaced}
                  onChange={(e) => updateMaterialByProduct(index, 'replaced', e.target.value as string)}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      </>
    );
  }
};
