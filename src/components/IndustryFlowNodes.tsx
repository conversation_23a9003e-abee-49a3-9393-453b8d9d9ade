import React from "react";
import { Handle, Position } from "@xyflow/react";

export function CustomNode({ data }) {
  return (
    <div
      style={{
        width: 180,
        minWidth: 180,
        minHeight: 60,
        padding: '16px 26px',
        height: 'auto',
        background: '#D6BCFA',
        borderRadius: '14px',
        border: '2px solid #9b87f5',
        color: '#222',
        fontWeight: 500,
        fontSize: 16,
        boxShadow: '0 4px 24px 0 rgba(126,105,171,0.10)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        letterSpacing: 0,
        textAlign: "center",
        position: "relative",
      }}
    >
      {/* Left handle - reduced gap for better visual continuity */}
      <Handle 
        type="target" 
        position={Position.Left} 
        id="left-target"
        style={{
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          left: -7,
          top: '50%',
          transform: 'translateY(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />
      
      {/* Left source handle */}
      <Handle 
        type="source" 
        position={Position.Left} 
        id="left-source"
        style={{
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          left: -7,
          top: '28%',
          transform: 'translateY(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />

      {/* Right handle - reduced gap for better visual continuity */}
      <Handle 
        type="source" 
        position={Position.Right} 
        id="right-source"
        style={{
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          right: -7,
          top: '50%',
          transform: 'translateY(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />
      
      {/* Right target handle */}
      <Handle 
        type="target" 
        position={Position.Right} 
        id="right-target"
        style={{
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          right: -7,
          top: '28%',
          transform: 'translateY(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />
      
      {/* Bottom source handle - reduced gap */}
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom-source"
        style={{
          left: '50%',
          bottom: -7,
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          top: 'auto',
          transform: 'translateX(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />
      
      {/* Bottom target handle */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="bottom-target"
        style={{
          left: '70%',
          bottom: -7,
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          top: 'auto',
          transform: 'translateX(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />
      
      {/* Top target handle - reduced gap */}
      <Handle
        type="target"
        position={Position.Top}
        id="top-target"
        style={{
          left: '50%',
          top: -7,
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          transform: 'translateX(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />
      
      {/* Top source handle */}
      <Handle
        type="source"
        position={Position.Top}
        id="top-source"
        style={{
          left: '70%',
          top: -7,
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #9b87f5',
          borderRadius: '50%',
          transform: 'translateX(-50%)',
          transition: 'transform 0.2s, background 0.2s',
          boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
        }}
        className="connection-handle"
      />

      <div style={{ width: '100%' }}>{data.label}</div>
    </div>
  );
}

// Helper function to get technology name from ID
// We're keeping this function for reference, though it's no longer used in CustomNode
function getTechnologyName(technologyId: string) {
  const technologies = {
    'tech1': 'Thermal Processing',
    'tech2': 'Chemical Processing',
    'tech3': 'Mechanical Processing',
    'tech4': 'Digital Automation',
    'tech5': 'Catalytic Conversion',
  };
  
  return technologies[technologyId] || technologyId;
}

export function InputDotNode() {
  return (
    <div style={{
      width: 15,
      height: 15,
      borderRadius: '50%',
      background: '#fff',
      border: '1.6px solid #9b87f5',
      margin: 'auto',
      marginTop: 8,
      marginLeft: 0,
      boxSizing: 'border-box',
      boxShadow: '0 2px 4px rgba(155, 135, 245, 0.3)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Handle
        type="source"
        position={Position.Right}
        id="source"
        style={{
          width: 10,
          height: 10,
          background: '#fff',
          border: '1.5px solid #9b87f5',
          right: -14,
          borderRadius: '50%',
        }}
      />
    </div>
  );
}

export function InputNode({ data }) {
  return (
    <div
      style={{
        width: 1,
        height: 1,
        opacity: 0,
        pointerEvents: 'none',
      }}
    >
      {/* Invisible source handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="right-source"
        style={{
          width: 1,
          height: 1,
          opacity: 0,
          pointerEvents: 'none',
        }}
      />
    </div>
  );
}

export function FinalOutputNode({ data }) {
  // Get output material if available
  const outputMaterial = data.outputs?.material || data.label;

  return (
    <div
      style={{
        width: 220,
        minWidth: 200,
        minHeight: 80,
        padding: '20px 28px',
        height: 'auto',
        background: 'linear-gradient(135deg, #FEF3C7 0%, #FBBF24 100%)', // Golden gradient
        borderRadius: '16px',
        border: '3px solid #F59E0B', // Golden border
        color: '#92400E', // Dark amber text
        fontWeight: 700,
        fontSize: 18,
        boxShadow: '0 8px 32px 0 rgba(245,158,11,0.25), 0 0 0 1px rgba(245,158,11,0.1)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        letterSpacing: 0,
        textAlign: "center",
        position: "relative",
        transform: 'scale(1)',
        transition: 'all 0.2s ease-in-out',
      }}
      className="final-output-node"
    >
      {/* Left target handle */}
      <Handle 
        type="target" 
        position={Position.Left} 
        id="left-target"
        style={{
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #FB923C',
          borderRadius: '50%',
          left: -7,
          top: '50%',
          transform: 'translateY(-50%)',
          boxShadow: '0 2px 4px rgba(251, 146, 60, 0.3)',
        }}
      />
      
      {/* Right target handle - for flexibility */}
      <Handle 
        type="target" 
        position={Position.Right} 
        id="right-target"
        style={{
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #FB923C',
          borderRadius: '50%',
          right: -7,
          top: '50%',
          transform: 'translateY(-50%)',
          boxShadow: '0 2px 4px rgba(251, 146, 60, 0.3)',
        }}
      />

      {/* Top target handle */}
      <Handle
        type="target"
        position={Position.Top}
        id="top-target"
        style={{
          left: '50%',
          top: -7,
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #FB923C',
          borderRadius: '50%',
          transform: 'translateX(-50%)',
          boxShadow: '0 2px 4px rgba(251, 146, 60, 0.3)',
        }}
      />
      
      {/* Bottom target handle - for flexibility */}
      <Handle
        type="target"
        position={Position.Bottom}
        id="bottom-target"
        style={{
          left: '50%',
          bottom: -7,
          width: 14,
          height: 14,
          background: '#fff',
          border: '2px solid #FB923C',
          borderRadius: '50%',
          transform: 'translateX(-50%)',
          boxShadow: '0 2px 4px rgba(251, 146, 60, 0.3)',
        }}
      />

      {/* Crown icon for final output */}
      <div style={{
        position: 'absolute',
        top: -12,
        right: -12,
        width: 24,
        height: 24,
        background: '#F59E0B',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: 'white',
        fontWeight: 'bold',
        boxShadow: '0 2px 8px rgba(245,158,11,0.3)'
      }}>
        ★
      </div>

      {/* Main content */}
      <div style={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '8px'
      }}>
        <div style={{
          fontSize: '16px',
          fontWeight: 600,
          color: '#92400E',
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}>
          FINAL PRODUCT
        </div>
        <div style={{
          fontSize: '20px',
          fontWeight: 700,
          color: '#78350F',
          textShadow: '0 1px 2px rgba(0,0,0,0.1)'
        }}>
          {outputMaterial}
        </div>
      </div>
    </div>
  );
}

// Add CSS for handle hover effects to main.tsx
