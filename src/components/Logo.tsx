
import React from 'react';
import { useTheme } from '@/components/ThemeProvider';

interface LogoProps {
  className?: string;
  theme?: 'dark' | 'light'; // Optional theme prop to override global theme
}

const Logo: React.FC<LogoProps> = ({ className, theme: propTheme }) => {
  const { theme: contextTheme } = useTheme();
  
  // Use prop theme if provided, otherwise use context theme
  const currentTheme = propTheme || contextTheme;

  return (
    <a
      href="https://www.recre8.earth"
      target="_blank"
      rel="noopener noreferrer"
      className={`flex items-center ${className}`}
    >
      <img
        src={currentTheme === 'dark' ? '/logo-light.svg' : '/logo-dark.svg'}
        alt="Recre8"
        className="h-8 md:h-10 w-auto"
      />
    </a>
  );
};

export default Logo;
