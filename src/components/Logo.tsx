
import { PUBLIC_WEBSITE } from '@/utils/endPoints';
import React from 'react';

interface LogoProps {
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ className }) => {
  return (
    <a href={PUBLIC_WEBSITE} target="_blank" rel="noopener" className={`flex items-center ${className}`}>
      <h1 className="text-2xl md:text-3xl font-bold">
        <span className="text-recrea-green">recre8</span>
        <span className="dark:text-white text-recrea-dark">.earth</span>
      </h1>
    </a>
  );
};

export default Logo;
