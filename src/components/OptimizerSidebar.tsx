import React, { useState } from "react";
import { useLocation } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { CreateScenarioModal } from './CreateScenarioModal';
import { FlowDiagramResponse } from '@/services/flowDiagramApi';
import { 
  Save, 
  FolderO<PERSON>, 
  FilePlus, 
  Plus, 
  Play, 
  BarChart3, 
  ArrowLeft,
  Settings,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Target,
  Package,
  Edit
} from "lucide-react";

interface SavedFlow {
  title: string;
  nodes: object[];
  edges: object[];
  flowType?: 'inventory' | 'scenario';
}

interface OptimizerSidebarProps {
  isScenarioMode?: boolean;
  hasConstraints?: boolean;
  hasRunScenario?: boolean;
  isRunningScenario?: boolean;
  scenarioName?: string;
  setScenarioName?: (name: string) => void;
  editingScenarioName?: boolean;
  setEditingScenarioName?: (editing: boolean) => void;
  handleScenarioNameChange?: (name: string) => void;
  savedFlows?: SavedFlow[];
  flowDiagrams?: FlowDiagramResponse[];
  isLoadingFlowDiagrams?: boolean;
  onSaveInventory?: () => void;
  onLoadInventory?: (flow?: unknown) => void;
  onCreateScenario?: (scenarioData?: unknown) => void;
  onAddConstraints?: () => void;
  onRunScenario?: () => void;
  onViewResults?: () => void;

  onSaveScenario?: () => void;
  onLoadScenario?: () => void;
  onSaveFlowDiagram?: () => void;
  onLoadFlowDiagram?: (id: string) => void;
  onRefreshFlowDiagrams?: () => void;
  currentFlowDiagramUuid?: string | null; // Add current flow diagram UUID
}

const OptimizerSidebar: React.FC<OptimizerSidebarProps> = ({
  isScenarioMode = false,
  hasConstraints = false,
  hasRunScenario = false,
  isRunningScenario = false,
  scenarioName = "",
  setScenarioName,
  editingScenarioName = false,
  setEditingScenarioName,
  handleScenarioNameChange,
  savedFlows = [],
  flowDiagrams = [],
  isLoadingFlowDiagrams = false,
  onSaveInventory,
  onLoadInventory,
  onCreateScenario,
  onAddConstraints,
  onRunScenario,
  onViewResults,

  onSaveScenario,
  onLoadScenario,
  onSaveFlowDiagram,
  onLoadFlowDiagram,
  onRefreshFlowDiagrams,
  currentFlowDiagramUuid,
}) => {
  const location = useLocation();
  const [saveAsDialogOpen, setSaveAsDialogOpen] = useState(false);
  const [newFlowName, setNewFlowName] = useState('');
  const [saveAsError, setSaveAsError] = useState('');
  const [createScenarioModalOpen, setCreateScenarioModalOpen] = useState(false);

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleScenarioNameChange?.(e.currentTarget.value);
      setEditingScenarioName?.(false);
    }
    if (e.key === 'Escape') {
      setEditingScenarioName?.(false);
    }
  };

  const openSaveAsDialog = () => {
    setNewFlowName(scenarioName + ' - Copy');
    setSaveAsError('');
    setSaveAsDialogOpen(true);
  };

  const handleSaveAs = () => {
    if (!newFlowName.trim()) {
      setSaveAsError('Flow name cannot be empty');
      return;
    }

    // Check if the name already exists
    const nameExists = savedFlows.some(flow => flow.title === newFlowName);
    if (nameExists) {
      setSaveAsError('A flow with this name already exists');
      return;
    }

    // Handle the save as operation by updating the scenario name and then saving
    setScenarioName?.(newFlowName);
    handleScenarioNameChange?.(newFlowName);
    setSaveAsDialogOpen(false);
    
    // Add a small delay to ensure name is updated before saving
    setTimeout(() => {
      onSaveFlowDiagram?.();
    }, 100);
  };

  const handleCreateScenarioClick = () => {
    setCreateScenarioModalOpen(true);
  };

  const handleCreateScenarioSubmit = (scenarioData: unknown) => {
    console.log('Creating scenario with data:', scenarioData);

    // Call the parent's onCreateScenario function with the scenario data
    if (onCreateScenario) {
      onCreateScenario(scenarioData);
    }

    setCreateScenarioModalOpen(false);
  };

  return (
    <Sidebar>
      <SidebarHeader className="p-4 flex items-center justify-center">
        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-recrea-turquoise to-recrea-teal flex items-center justify-center text-white font-bold">
          AI
        </div>
      </SidebarHeader>
      
      <SidebarContent className="p-4">
        {/* Name Section */}
        <SidebarGroup>
          <div className="text-sm font-semibold px-2 mb-3 text-[#7E69AB]">
            {isScenarioMode ? "Scenario Name" : "Inventory Name"}
          </div>
          <SidebarGroupContent>
            {/* Name input only, no inline Save button */}
            <div className="mb-2">
              {editingScenarioName ? (
                <Input
                  type="text"
                  className="text-sm border border-[#ded3fd] rounded px-2 py-1 bg-white text-[#181727]"
                  value={scenarioName}
                  onChange={e => setScenarioName?.(e.target.value)}
                  onBlur={e => {
                    handleScenarioNameChange?.(e.target.value);
                    setEditingScenarioName?.(false);
                  }}
                  onKeyDown={handleInputKeyDown}
                  placeholder={isScenarioMode ? "Rename your scenario" : "Rename your inventory"}
                  autoFocus
                />
              ) : (
                <div
                  className="text-sm cursor-pointer hover:bg-[#f5f2fc] rounded px-2 py-1 border border-transparent hover:border-[#ded3fd] flex items-center gap-2"
                  onClick={() => setEditingScenarioName?.(true)}
                  title="Click to edit name"
                >
                  <span className="flex-1">
                    {scenarioName || (isScenarioMode ? "Rename your scenario" : "Rename your inventory")}
                  </span>
                  <Edit size={12} className="text-gray-400" />
                </div>
              )}
            </div>
            {/* Save and Open buttons stacked */}
            <Button
              variant="outline"
              className="w-full justify-start gap-2 h-10 border-green-300 text-green-700 hover:bg-green-50 mb-2"
              onClick={onSaveFlowDiagram}
              title="Save"
            >
              <Save size={16} />
              Save
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start gap-2 h-10 border-blue-300 text-blue-700 hover:bg-blue-50 mb-2"
                >
                  <FolderOpen size={16} />
                  Open
                  {isLoadingFlowDiagrams && <RefreshCw size={12} className="animate-spin ml-auto" />}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-80">
                <div className="px-3 py-2 border-b">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-semibold text-gray-700">
                      {isScenarioMode ? 'Saved Scenarios' : 'Saved Inventories'}
                    </span>
                    {onRefreshFlowDiagrams && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-xs"
                        onClick={onRefreshFlowDiagrams}
                      >
                        <RefreshCw size={12} className="mr-1" />
                        Refresh
                      </Button>
                    )}
                  </div>
                </div>
                {isLoadingFlowDiagrams ? (
                  <div className="px-3 py-6 text-center">
                    <RefreshCw size={16} className="animate-spin mx-auto mb-2 text-gray-400" />
                    <div className="text-sm text-gray-500">
                      {isScenarioMode ? 'Loading scenarios...' : 'Loading inventories...'}
                    </div>
                  </div>
                ) : flowDiagrams && flowDiagrams.length > 0 ? (
                  <div className="max-h-64 overflow-y-auto">
                    {(() => {
                      const filteredDiagrams = flowDiagrams.filter((diagram) => {
                        // Filter based on current mode
                        if (isScenarioMode) {
                          // In scenario mode, show only SCENARIO_MAIN flows
                          return diagram.flow_type === 'SCENARIO_MAIN';
                        } else {
                          // In inventory mode, show only INVENTORY_MAIN flows
                          return diagram.flow_type === 'INVENTORY_MAIN';
                        }
                      });
                      
                      return filteredDiagrams.map((diagram, index) => {
                        // Use UUID if available, otherwise use name-based ID to avoid index confusion
                        const diagramId = diagram.uuid || `name-${diagram.name}`;
                      return (
                        <DropdownMenuItem
                          key={diagramId}
                          onClick={() => onLoadFlowDiagram?.(diagramId)}
                          className="px-3 py-3 cursor-pointer hover:bg-gray-50"
                        >
                          <div className="flex items-center justify-between w-full">
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-gray-900 truncate text-sm">
                                {diagram.name}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                {(diagram.tag || diagram.flow_type || 'Unknown').replace('_', ' ')} • {diagram.created_at ? new Date(diagram.created_at).toLocaleDateString() : 'Unknown'}
                              </div>
                            </div>
                            <Badge
                              variant={(diagram.tag || diagram.flow_type || '').startsWith('SCENARIO') ? 'default' : 'secondary'}
                              className="ml-3 flex items-center gap-1 text-xs"
                            >
                              {(diagram.tag || diagram.flow_type || '').startsWith('SCENARIO') ? (
                                <>
                                  <Target size={10} />
                                  Scenario
                                </>
                              ) : (
                                <>
                                  <Package size={10} />
                                  Inventory
                                </>
                              )}
                            </Badge>
                          </div>
                        </DropdownMenuItem>
                      );
                    });
                  })()}
                  </div>
                ) : (
                  <div className="px-3 py-6 text-center">
                    <FolderOpen size={24} className="mx-auto mb-2 text-gray-300" />
                    <div className="text-sm text-gray-500 mb-1">
                      {isScenarioMode ? 'No saved scenarios' : 'No saved inventories'}
                    </div>
                    <div className="text-xs text-gray-400">
                      {isScenarioMode ? 'Save your current scenario to see it here' : 'Save your current inventory to see it here'}
                    </div>
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {!isScenarioMode ? (
          // Inventory Mode
          <SidebarGroup>
            <div className="text-sm font-semibold px-2 mb-3 text-[#7E69AB]">Inventory Actions</div>
            <SidebarGroupContent>
              <div className="space-y-2">
                <Button
                  variant="default"
                  className="w-full justify-start gap-2 h-10 bg-[#9b87f5] hover:bg-[#7E69AB]"
                  onClick={handleCreateScenarioClick}
                >
                  <FilePlus size={16} />
                  Create Scenario
                </Button>
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        ) : (
          // Scenario Mode
          <div className="space-y-4">
            <SidebarGroup>
              <div className="text-sm font-semibold px-2 mb-3 text-[#7E69AB]">Scenario Actions</div>
              <SidebarGroupContent>
                <div className="space-y-2">
                  {/* Add Constraints - Primary action */}
                  <Button
                    variant={hasConstraints ? "outline" : "default"}
                    className={`w-full justify-start gap-2 h-10 ${
                      hasConstraints
                        ? "border-green-500 text-green-700 bg-green-50"
                        : "bg-[#9b87f5] hover:bg-[#7E69AB]"
                    }`}
                    onClick={onAddConstraints}
                  >
                    {hasConstraints ? <CheckCircle size={16} /> : <Plus size={16} />}
                    {hasConstraints ? "Constraints Added" : "Add Constraints"}
                  </Button>

                  {/* Run Scenario - Disabled until constraints added */}
                  <Button
                    variant={hasRunScenario ? "outline" : "default"}
                    className={`w-full justify-start gap-2 h-10 ${
                      !hasConstraints
                        ? "opacity-50 cursor-not-allowed"
                        : hasRunScenario
                        ? "border-green-500 text-green-700 bg-green-50"
                        : isRunningScenario
                        ? "bg-orange-500 hover:bg-orange-600"
                        : "bg-green-500 hover:bg-green-600"
                    }`}
                    onClick={hasConstraints ? onRunScenario : undefined}
                    disabled={!hasConstraints || isRunningScenario}
                    title={!hasConstraints ? "Add constraints before running scenario" : isRunningScenario ? "Scenario is running..." : ""}
                  >
                    {hasRunScenario ? (
                      <CheckCircle size={16} />
                    ) : isRunningScenario ? (
                      <RefreshCw size={16} className="animate-spin" />
                    ) : (
                      <Play size={16} />
                    )}
                    {hasRunScenario ? "Scenario Complete" : isRunningScenario ? "Running..." : "Run Scenario"}
                  </Button>

                  {/* View Results - Disabled until scenario run */}
                  <Button
                    variant="outline"
                    className={`w-full justify-start gap-2 h-10 ${
                      !hasRunScenario ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    onClick={hasRunScenario ? onViewResults : undefined}
                    disabled={!hasRunScenario}
                    title={!hasRunScenario ? "Run scenario to view results" : ""}
                  >
                    <BarChart3 size={16} />
                    View Results
                  </Button>
                </div>
              </SidebarGroupContent>
            </SidebarGroup>


          </div>
        )}

        {/* Status Indicators */}
        {isScenarioMode && (
          <SidebarGroup>
            <div className="text-sm font-semibold px-2 mb-3 text-[#7E69AB]">Status</div>
            <SidebarGroupContent>
              <div className="space-y-2 text-xs">
                <div className={`flex items-center gap-2 p-2 rounded ${
                  hasConstraints ? "bg-green-50 text-green-700" : "bg-gray-50 text-gray-500"
                }`}>
                  {hasConstraints ? <CheckCircle size={12} /> : <AlertCircle size={12} />}
                  Constraints {hasConstraints ? "Added" : "Required"}
                </div>

                <div className={`flex items-center gap-2 p-2 rounded ${
                  hasRunScenario ? "bg-green-50 text-green-700" : "bg-gray-50 text-gray-500"
                }`}>
                  {hasRunScenario ? <CheckCircle size={12} /> : <AlertCircle size={12} />}
                  Scenario {hasRunScenario ? "Complete" : "Pending"}
                </div>
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Save As Dialog */}
        <Dialog open={saveAsDialogOpen} onOpenChange={setSaveAsDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Save Flow As</DialogTitle>
              <DialogDescription>
                Enter a new name for your flow.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newFlowName}
                  onChange={(e) => {
                    setNewFlowName(e.target.value);
                    setSaveAsError('');
                  }}
                  className="col-span-3"
                  autoFocus
                />
              </div>
              {saveAsError && (
                <div className="text-sm text-red-500 ml-[80px]">{saveAsError}</div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSaveAsDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveAs}>Save</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Create Scenario Modal */}
        <CreateScenarioModal
          open={createScenarioModalOpen}
          onClose={() => setCreateScenarioModalOpen(false)}
          onCreateScenario={handleCreateScenarioSubmit}
          savedFlows={savedFlows}
          flowDiagrams={flowDiagrams}
          onRefreshFlowDiagrams={onRefreshFlowDiagrams}
          currentFlowDiagramUuid={currentFlowDiagramUuid}
          currentScenarioName={scenarioName}
        />
      </SidebarContent>
    </Sidebar>
  );
};

export default OptimizerSidebar;
