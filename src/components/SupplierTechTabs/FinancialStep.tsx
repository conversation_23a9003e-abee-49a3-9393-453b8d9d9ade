
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { FormData, OutputForm } from "./types";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Capacity units suitable for industrial processes
const capacityUnits = [
  "Tonnes/day",
  "Tonnes/hour",
  "Tonnes/year",
  "MW",
  "GW",
  "m³/day",
  "m³/hour",
  "Barrels/day",
  "Units/day",
  "Units/hour"
];

interface FinancialStepProps {
  formData: FormData;
  updateFormField: (section: string, field: string, value: string) => void;
  errors: Record<string, string>;
  outputs: OutputForm[];
  availableNodes?: Array<{ id: string; data: { label?: string } }>;
  technologies: string[];
  activeTechnology: string;
  setActiveTechnology: (tech: string) => void;
  readOnly?: boolean;
}

export const FinancialStep: React.FC<FinancialStepProps> = ({
  formData,
  updateFormField,
  errors,
  outputs,
  availableNodes = [],
  technologies,
  activeTechnology,
  setActiveTechnology,
  readOnly = false
}) => {
  // Get the financial data for the current active technology
  const getFinancialData = () => {
    // For existing data, use the main financial data as the source of truth
    // and ensure all fields are properly mapped with backward compatibility
    const mainFinancial = formData.financial;
    if (mainFinancial && (mainFinancial.capacity || mainFinancial.capitalCost || mainFinancial.operatingMaintenanceCost)) {
      return {
        capacity: mainFinancial.capacity || "",
        capacityUnit: mainFinancial.capacityUnit || "Tonnes/day",
        capitalCost: mainFinancial.capitalCost || mainFinancial.capitalCostUnit || "", // Backward compatibility
        operatingMaintenanceCost: mainFinancial.operatingMaintenanceCost || mainFinancial.omCost || "" // Backward compatibility
      };
    }

    // Final fallback for completely new entries
    return {
      capacity: "",
      capacityUnit: "Tonnes/day",
      capitalCost: "",
      operatingMaintenanceCost: ""
    };
  };

  // Update financial data for the current technology
  const updateFinancial = (field: string, value: string) => {
    updateFormField('financial', field, value);
  };

  const financialData = getFinancialData();

  return (
    <>
      <div className="font-semibold mb-2">Financial Information</div>
      
      <Tabs value={activeTechnology} onValueChange={setActiveTechnology} className="w-full">
        {technologies.map((tech) => (
          <TabsContent key={tech} value={tech}>
            <div className="border-2 border-green-200 rounded-md mb-3 p-3">
              <div className="font-semibold mb-1">Financial Information</div>
              
              <div className="flex flex-col md:flex-row gap-2">
                <div className="flex-1 min-w-0">
                  <label>Capacity</label>
                  <div className="flex gap-2">
                    <Input
                      value={financialData.capacity}
                      onChange={(e) => updateFinancial('capacity', e.target.value)}
                      placeholder="Enter capacity"
                      className="flex-1"
                      disabled={readOnly}
                    />
                    {readOnly ? (
                      <div className="w-32 border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300">
                        {financialData.capacityUnit || "Tonnes/day"}
                      </div>
                    ) : (
                      <Select
                        value={financialData.capacityUnit || "Tonnes/day"}
                        onValueChange={(value) => updateFinancial('capacityUnit', value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="Unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {capacityUnits.map(unit => (
                            <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <label>Capital Cost/ Unit Capacity</label>
                  <Input
                    value={financialData.capitalCost}
                    onChange={(e) => updateFinancial('capitalCost', e.target.value)}
                    placeholder="Enter cost per unit"
                    disabled={readOnly}
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <label>Annual O&M Cost</label>
                  <Input
                    value={financialData.operatingMaintenanceCost}
                    onChange={(e) => updateFinancial('operatingMaintenanceCost', e.target.value)}
                    placeholder="Enter Annual O&M cost"
                    disabled={readOnly}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </>
  );
};
