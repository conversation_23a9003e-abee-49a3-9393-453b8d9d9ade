import React, { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogClose } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { useToastContext } from "@/contexts/ToastContext";

// Import our created components
import { ByProductsStep } from "@/components/SupplierTechTabs/ByProductsStep";
import { InputsStep } from "@/components/SupplierTechTabs/InputsStep";
import { FinancialStep } from "@/components/SupplierTechTabs/FinancialStep";
import { StepNavigation } from "@/components/SupplierTechTabs/StepNavigation";
import { processConnectionsFromFormData } from "@/utils/connectionUtils";
import {
  ConnectionFormProps,
  OutputForm,
  FormData,
  SUPPLIER_TECH_STEPS,
  StepId,
  EnergyOutput,
  MaterialOutput,
  EnergyInput,
  EmissionInput,
  MaterialInput,
  emissions
} from "@/components/SupplierTechTabs/types";
import { useConnectionFormData } from "@/hooks/useConnectionFormData";
import { useSectors } from "@/hooks/useSectors";
import { getSectorUuidFromIndustryId } from "@/services/activitiesApi";
import { findActivityUuidByName } from "@/services/connectionFormApi";
import { OutputsStep } from "./SupplierTechTabs/OutputsStep";

export function SupplierTechDialog({
  open,
  onClose,
  onComplete,
  autoFillInputs = [],
  sourceNode = null,
  targetNode = null,
  availableNodes = [],
  incomingConnectionData = null,
  existingFormData = null,
  sectorUuid,
  activityUuid,
  industryId,
  supplierCompanyName,
  technologiesName,
}: ConnectionFormProps & { supplierCompanyName?: string }) {
  const { toast } = useToastContext();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // API data hooks
  const { sectors } = useSectors();
  const [resolvedSectorUuid, setResolvedSectorUuid] = useState<string | undefined>(sectorUuid);
  const [resolvedActivityUuid, setResolvedActivityUuid] = useState<string | undefined>(activityUuid);

  // Use API data hooks
  const apiData = useConnectionFormData(resolvedActivityUuid, resolvedSectorUuid);

  // State for managing multiple outputs
  const [outputs, setOutputs] = useState<OutputForm[]>([]);
  const [activeOutputTab, setActiveOutputTab] = useState("output-0");


  // State for managing technologies across all tabs
  const [technologies, setTechnologies] = useState<string[]>([technologiesName ||"Technology 1"]);
  const [activeTechnology, setActiveTechnology] = useState(technologiesName || "Technology 1");

  // State for technology-specific form data
  const [technologyFormData, setTechnologyFormData] = useState<Record<string, FormData>>({});

  // State to track available technologies from outputs
  const [availableTechnologies, setAvailableTechnologies] = useState<string[]>([]);
  
  // Flag to track if form should be submitted (only after completing all steps)
  const [shouldCompleteForm, setShouldCompleteForm] = useState(false);

  // Check if technology is selected (required for proceeding to other steps)
  const isTechnologySelected = () => {
    const currentTechData = getCurrentTechnologyFormData();
    return currentTechData.technology && currentTechData.technology.trim() !== "";
  };

  // Get selected activity name from sourceNode for display in dialog header
  const selectedActivityName = (sourceNode as any)?.data?.label || "New Activity";

  // Resolve sector UUID from industry ID if not provided
  useEffect(() => {
    const resolveSectorUuid = async () => {
      if (sectorUuid) {
        setResolvedSectorUuid(sectorUuid);
        return;
      }

      if (industryId && sectors.length > 0) {
        try {
          const uuid = await getSectorUuidFromIndustryId(industryId);
          setResolvedSectorUuid(uuid);
        } catch (error) {
          // Fallback to first sector if available
          if (sectors.length > 0) {
            setResolvedSectorUuid(sectors[0].uuid);
          }
        }
      }
    };

    resolveSectorUuid();
  }, [sectorUuid, industryId, sectors]);

  // Resolve activity UUID from activity name if not provided
  useEffect(() => {
    const resolveActivityUuid = async () => {
      if (activityUuid) {
        setResolvedActivityUuid(activityUuid);
        return;
      }

      // Try to get activity name from sourceNode
      const activityName = selectedActivityName;
      if (activityName && activityName !== "New Activity" && resolvedSectorUuid) {
        try {
          const uuid = await findActivityUuidByName(resolvedSectorUuid, activityName, { toast });
          if (uuid) {
            setResolvedActivityUuid(uuid);
            // console.log(`Resolved activity UUID: ${uuid} for activity: ${activityName}`);
          } else {
            console.log(`No activity UUID found for activity: ${activityName}`);
          }
        } catch (error) {
          console.error('Error resolving activity UUID:', error);
        }
      }
    };

    resolveActivityUuid();
  }, [activityUuid, selectedActivityName, resolvedSectorUuid, toast]);

  // Form data state
  const [formData, setFormData] = useState<FormData>({
    // Basic info
    activity: "",
    technology: "",
    // Technology life span
    startYear: "2000",
    endYear: "2089",
    // Keep for compatibility
    customTechnology: "",
    customActivity: "",
    
    // Step 1: Inputs (now Step 3) - Arrays for multiple entries
    energyInputs: [],
    emissions: [],
    materialInputs: [],
    
    // Legacy single entries - kept for compatibility
    energyInput: {
      source: "",
      unit: "GJ",
      cost: "",
      sec: ""
    },
    emission: {
      source: "",
      ef: "",
      unit: "GJ"
    },
    matInput: {
      material: "",
      unit: "Tonnes",
      cost: "",
      smc: ""
    },
    
    // Step 3: By-products (now Step 2)
    byproductTechnology: "", // Field for by-product technology
    byproductEnergy: [{
      byproduct: "",
      unit: "GJ",
      bppo: "",
      connect: "",
      replaced: ""
    }],
    byproductMat: [{
      byproduct: "",
      unit: "Tonnes",
      bppo: "",
      connect: "",
      replaced: "",
      techEmissionFactor: "",
      emissionFactor: "",
      emissionUnit: "Tonnes"
    }],
    
    // Step 4: Financial (remains Step 4)
    financial: {
      capacity: "",
      capacityUnit: "Tonnes/day",
      capitalCostUnit: "",
      omCost: ""
    },
    
    // NEW: Add financialEntries to store multiple financial entries
    financialEntries: {}
  });

  // UI helper states
  const [energyInputAutoFillLabel, setEnergyInputAutoFillLabel] = useState("");
  const [matInputAutoFillLabel, setMatInputAutoFillLabel] = useState("");
  const [technologyAutoFillLabel, setTechnologyAutoFillLabel] = useState(""); // State for technology auto-fill
  
   // Default initialization for new connections
   const initialEnergyOutput: EnergyOutput = {
    id: `energy-${Date.now()}-0`,
    energy: "",
    unit: "GJ",
    sec: "",
    final: false,
    connect: "",
    qty: "",
    qtyUnit: "GJ",
    destinationTechnology: "" // Initialize destination technology
  };

  const initialMatOutput: MaterialOutput = {
    id: `material-${Date.now()}-0`,
    material: "",
    unit: "Tonnes",
    smc: "",
    final: false,
    connect: "", // Ensure connect property is initialized
    qty: "",
    qtyUnit: "Tonnes",
    destinationTechnology: "" // Initialize destination technology
  };

  const initialOutput: OutputForm = useMemo(() => ({
    id: "output-0",
    targetNode: targetNode?.id || "",
    outputTechnology: technologiesName || "Technology 1",
    energyOutputs: [initialEnergyOutput],
    matOutputs: [initialMatOutput]
  }), [technologiesName]);


  // Initialize with one default output tab when dialog opens
  useEffect(() => {
    if (open) {
      // Check if we have existing form data to load
      if (existingFormData?.details) {
        // Load existing outputs
        if (existingFormData.outputs && existingFormData.outputs.length > 0) {
          setOutputs(existingFormData.outputs);
          setActiveOutputTab(existingFormData.outputs[0].id);
        }

        // Load existing technologies
        if (existingFormData.technologies && existingFormData.technologies.length > 0) {
          setTechnologies(existingFormData.technologies);
          setActiveTechnology(existingFormData.technologies[0]);
        }

        // Load existing technology form data
        if (existingFormData.technologyFormData) {
          setTechnologyFormData(existingFormData.technologyFormData);
        }

        // Load existing global form data
        if (existingFormData) {

          setFormData({  
            // Basic info
            activity: "",
            technology: "",
            // Technology life span
            startYear: String(existingFormData?.details?.startYear || "2025"),
            endYear: String(existingFormData?.details?.endYear || "2035"),
            // Keep for compatibility
            customTechnology: "",
            customActivity: "",
            
            // Step 1: Inputs (now Step 3) - Arrays for multiple entries
            energyInputs: (existingFormData?.details?.inputs?.energies || []).map(
              (item: any, idx: number) => ({ id: item.id || `energy-${idx}`, ...item })
            ),
            emissions: (existingFormData?.details?.inputs?.emissions || []).map(
              (item: any, idx: number) => ({ id: item.id || `emission-${idx}`, factor: item.factor ?? item.ef ?? '', ...item })
            ),
            materialInputs: (existingFormData?.details?.inputs?.materials || []).map(
              (item: any, idx: number) => ({ id: item.id || `material-${idx}`, smc: item.smc ?? '', ...item })
            ),
            
            // Legacy single entries - kept for compatibility
            energyInput: existingFormData?.details?.outputs?.energies?.[0] || {
              source: "",
              unit: "GJ",
              cost: "",
              sec: ""
            },
            emission: existingFormData?.details?.outputs?.emissions?.[0] || {
              source: "",
              ef: "",
              unit: "GJ"
            },
            matInput: {
              ...(existingFormData?.details?.outputs?.materials?.[0] || {}),
              cost: existingFormData?.details?.outputs?.materials?.[0]?.cost ?? "",
              smc: existingFormData?.details?.outputs?.materials?.[0]?.smc ?? "",
              material: existingFormData?.details?.outputs?.materials?.[0]?.material ?? "",
              unit: existingFormData?.details?.outputs?.materials?.[0]?.unit ?? "Tonnes",
            },
            
            // Step 3: By-products (now Step 2)
            byproductTechnology: "", // Field for by-product technology
            byproductEnergy: existingFormData?.details?.byproducts?.energies || [{
              byproduct: "",
              unit: "GJ",
              bppo: "",
              connect: "",
              replaced: ""
            }],
            byproductMat: existingFormData?.details?.byproducts?.materials || [{
              byproduct: "",
              unit: "Tonnes",
              bppo: "",
              connect: "",
              replaced: "",
              techEmissionFactor: "",
              emissionFactor: "",
              emissionUnit: "Tonnes"
            }],
            
            // Step 4: Financial (remains Step 4)
            financial: {
              capacity: String(existingFormData?.details?.financial?.capacity ?? ""),
              capacityUnit: String(existingFormData?.details?.financial?.capacityUnit ?? "Tonnes/day"),
              // capitalCost: String(existingFormData?.details?.financial?.capitalCost ?? ""),
              capitalCostUnit: String(existingFormData?.details?.financial?.capitalCostUnit ?? ""),
              omCost: String(existingFormData?.details?.financial?.operatingMaintenanceCost ?? ""),
              // omCostUnit: String(existingFormData?.details?.financial?.operatingMaintenanceCostUnit ?? "")
            },
            
            // NEW: Add financialEntries to store multiple financial entries
            financialEntries: {}
          });
        }

        setOutputs([
          {
            id: "output-0",
            targetNode: targetNode?.id || "",
            outputTechnology: technologiesName ||"Technology 1",
            energyOutputs: existingFormData?.details?.outputs?.energies || [initialEnergyOutput],
            matOutputs: (existingFormData?.details?.outputs?.materials || [initialMatOutput]).map((item: any, idx: number) => ({
              id: item.id ?? `material-${idx}`,
              material: item.material ?? '',
              unit: item.unit ?? 'Tonnes',
              smc: item.smc ?? '',
              final: item.final ?? false,
              connect: item.connect ?? '',
              qty: item.qty ?? '',
              qtyUnit: item.qtyUnit ?? 'Tonnes',
              destinationTechnology: item.destinationTechnology ?? ''
            }))
          }
        ]);


        return; // Skip default initialization if we loaded existing data
      }

     
      setOutputs([initialOutput]);
      setActiveOutputTab("output-0");
      setTechnologies([technologiesName || "Technology 1"]);
      setActiveTechnology(technologiesName || "Technology 1");

      // Initialize technology form data with proper structure
      const initialTechFormData: FormData = {
        technology: "",
        activity: "",
        startYear: "2000",
        endYear: "2071",
        customTechnology: "",
        customActivity: "",
        // Arrays for multiple entries (actual form data)
        energyInputs: [],
        emissions: [],
        materialInputs: [],
        // Legacy single entries (for compatibility)
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        // By-products
        byproductTechnology: technologiesName || "Technology 1",
        byproductEnergy: [{ byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" }],
        byproductMat: [{ byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" }],
        energyByProducts: [],
        materialByProducts: [],
        // Financial
        financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCostUnit: "", omCost: "" },
        financialEntries: {}
      };

      // Initialize with one empty entry for each array
      const initialEnergyInput: EnergyInput = {
        id: `energy-${Date.now()}-0`,
        source: "",
        unit: "GJ",
        cost: "",
        sec: "",
        sourceActivity: "Nil",
        technology: "Nil"
      };

      const initialEmissionInput: EmissionInput = {
        id: `emission-${Date.now()}-0`,
        source: "",
        factor: "",
        unit: "kg"
      };

      const initialMaterialInput: MaterialInput = {
        id: `material-${Date.now()}-0`,
        material: "",
        unit: "Tonnes",
        cost: "",
        smc: "",
        sourceActivity: "Nil",
        technology: "Nil"
      };

      // Update the technology form data with initial arrays
      initialTechFormData.energyInputs = [initialEnergyInput];
      initialTechFormData.emissions = [initialEmissionInput];
      initialTechFormData.materialInputs = [initialMaterialInput];

      setTechnologyFormData({
        [technologiesName || "Technology 1"]: initialTechFormData
      });
    }
  }, [open, targetNode, existingFormData]);

  // Reset the form ONLY when dialog closes
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  // Track available technologies from inputs (since Inputs is now first)
  useEffect(() => {
    const uniqueTechnologies = new Set<string>();

    // Add technology from formData (from Inputs step)
    if (formData.technology) {
      uniqueTechnologies.add(formData.technology);
    }

    // Also add technologies from outputs for backward compatibility
    outputs.forEach(output => {
      if (output.outputTechnology) {
        uniqueTechnologies.add(output.outputTechnology);
      }
    });

    setAvailableTechnologies(Array.from(uniqueTechnologies));
  }, [formData.technology, outputs]);

  // Sync active technology changes with form data
  useEffect(() => {
    // Ensure we have form data for the active technology
    if (activeTechnology && !technologyFormData[activeTechnology]) {
      const initialFormData: FormData = {
        technology: activeTechnology,
        activity: "",
        startYear: String(existingFormData?.details?.startYear || "2025"),
        endYear: String(existingFormData?.details?.endYear || "2022"),
        // Keep for compatibility
        customTechnology: "",
        customActivity: "",
        
        // Step 1: Inputs (now Step 3) - Arrays for multiple entries
        energyInputs: (existingFormData?.details?.inputs?.energies || []).map(
          (item: any, idx: number) => ({ id: item.id || `energy-${idx}`, ...item })
        ),
        emissions: (existingFormData?.details?.inputs?.emissions || []).map(
          (item: any, idx: number) => ({ id: item.id || `emission-${idx}`, factor: item.factor ?? item.ef ?? '', ...item })
        ),
        materialInputs: (existingFormData?.details?.inputs?.materials || []).map(
          (item: any, idx: number) => ({ id: item.id || `material-${idx}`, smc: item.smc ?? '', ...item })
        ),
        
        // Legacy single entries - kept for compatibility
        energyInput: existingFormData?.details?.outputs?.energies?.[0] || {
          source: "",
          unit: "GJ",
          cost: "",
          sec: ""
        },
        emission: existingFormData?.details?.outputs?.emissions?.[0] || {
          source: "",
          ef: "",
          unit: "GJ"
        },
        matInput: {
          ...(existingFormData?.details?.outputs?.materials?.[0] || {}),
          cost: existingFormData?.details?.outputs?.materials?.[0]?.cost ?? "",
          smc: existingFormData?.details?.outputs?.materials?.[0]?.smc ?? "",
          material: existingFormData?.details?.outputs?.materials?.[0]?.material ?? "",
          unit: existingFormData?.details?.outputs?.materials?.[0]?.unit ?? "Tonnes",
        },
        
        // Step 3: By-products (now Step 2)
        byproductTechnology: "", // Field for by-product technology
        byproductEnergy: existingFormData?.details?.byproducts?.energies || [{
          byproduct: "",
          unit: "GJ",
          bppo: "",
          connect: "",
          replaced: ""
        }],
        byproductMat: existingFormData?.details?.byproducts?.materials || [{
          byproduct: "",
          unit: "Tonnes",
          bppo: "",
          connect: "",
          replaced: "",
          techEmissionFactor: "",
          emissionFactor: "",
          emissionUnit: "Tonnes"
        }],
        
        // Step 4: Financial (remains Step 4)
        financial: {
          capacity: String(existingFormData?.details?.financial?.capacity ?? ""),
          capacityUnit: String(existingFormData?.details?.financial?.capacityUnit ?? "Tonnes/day"),
          // capitalCost: String(existingFormData?.details?.financial?.capitalCost ?? ""),
          capitalCostUnit: String(existingFormData?.details?.financial?.capitalCostUnit ?? ""),
          omCost: String(existingFormData?.details?.financial?.operatingMaintenanceCost ?? ""),
          // omCostUnit: String(existingFormData?.details?.financial?.operatingMaintenanceCostUnit ?? "")
        },
        
        // NEW: Add financialEntries to store multiple financial entries
        financialEntries: {}
      };

      setTechnologyFormData(prev => ({
        ...prev,
        [activeTechnology]: initialFormData
      }));
    }
  }, [activeTechnology, technologyFormData]);

  // Sync activeOutputTab with activeTechnology
  useEffect(() => {
    // Find the output that corresponds to the current technology
    const currentTechOutput = outputs.find(o => o.outputTechnology === activeTechnology);
    if (currentTechOutput && activeOutputTab !== currentTechOutput.id) {
      setActiveOutputTab(currentTechOutput.id);
    }
  }, [activeTechnology, outputs, activeOutputTab]);

  // Check and apply incoming connection data for Node B inputs
  useEffect(() => {
    if (!open || !incomingConnectionData) return;
    
    try {
      // Only apply the incoming connection data if we're on inputs or byproducts step
      if (currentStepIndex !== 0 && currentStepIndex !== 2) return;
      
      // Apply technology from incoming connection to technology field
      if (incomingConnectionData.outputTechnology) {
        setFormData(prev => ({
          ...prev,
          technology: incomingConnectionData.outputTechnology,
          byproductTechnology: incomingConnectionData.outputTechnology // Also set by-product technology
        }));
        
        setTechnologyAutoFillLabel(`${incomingConnectionData.outputTechnology} (auto-filled from connection)`);
      }
      
      // Apply energy output from incoming connection to energy input
      if (incomingConnectionData.energyOutput?.energy) {
        setFormData(prev => ({
          ...prev,
          energyInput: {
            ...prev.energyInput,
            source: incomingConnectionData.energyOutput.energy,
            unit: incomingConnectionData.energyOutput.unit || "GJ", 
            sec: incomingConnectionData.energyOutput.sec || ""
          }
        }));
        
        setEnergyInputAutoFillLabel(`${incomingConnectionData.energyOutput.energy} (auto-filled from connection)`);
      }
      
      // Apply material output from incoming connection to material input
      if (incomingConnectionData.matOutput?.material) {
        setFormData(prev => ({
          ...prev,
          matInput: {
            ...prev.matInput,
            material: incomingConnectionData.matOutput.material,
            unit: incomingConnectionData.matOutput.unit || "Tonnes",
            smc: incomingConnectionData.matOutput.smc || ""
          }
        }));
        
        setMatInputAutoFillLabel(`${incomingConnectionData.matOutput.material} (auto-filled from connection)`);
      }
    } catch (err) {
      console.error("Error applying incoming connection data:", err);
    }
  }, [open, incomingConnectionData, currentStepIndex]);

  // Auto-fill inputs based on source node outputs
  useEffect(() => {
    if (!open || !autoFillInputs || autoFillInputs.length === 0) return;
    
    try {
      // Only apply if we're on the inputs or byproducts step
      if (currentStepIndex !== 0 && currentStepIndex !== 2) return;
      
      // Process technology from source node
      const techItems = autoFillInputs
        .filter(item => item && item.technology)
        .map(item => ({
          label: `${item.technology} (auto-filled from ${item.nodeName})`,
          value: item.technology
        }));
      
      // Process energy outputs from source node
      const energyItems = autoFillInputs
        .filter(item => item && item.outputs && item.outputs.energy)
        .map(item => ({
          label: `${item.outputs!.energy} (auto-filled from ${item.nodeName})`,
          value: item.outputs!.energy,
          unit: item.outputs!.energyUnit || "GJ",
          sec: item.outputs!.energySEC || ""
        }));
        
      // Process material outputs from source node
      const materialItems = autoFillInputs
        .filter(item => item && item.outputs && item.outputs.material)
        .map(item => ({
          label: `${item.outputs!.material} (auto-filled from ${item.nodeName})`,
          value: item.outputs!.material,
          unit: item.outputs!.materialUnit || "Tonnes",
          smc: item.outputs!.materialSMC || ""
        }));
      
      // Apply technology auto-fill if available and not already set by incoming connection
      if (techItems.length > 0 && !incomingConnectionData?.outputTechnology) {
        setFormData(prev => ({
          ...prev,
          technology: techItems[0].value,
          byproductTechnology: techItems[0].value
        }));
        setTechnologyAutoFillLabel(techItems[0].label);
      }
      
      // Apply energy auto-fill if available and not already set by incoming connection
      if (energyItems.length > 0 && !incomingConnectionData?.energyOutput?.energy) {
        setFormData(prev => ({
          ...prev,
          energyInput: {
            ...prev.energyInput,
            source: energyItems[0].value,
            unit: energyItems[0].unit,
            sec: energyItems[0].sec
          }
        }));
        setEnergyInputAutoFillLabel(energyItems[0].label);
      }
      
      // Apply material auto-fill if available and not already set by incoming connection
      if (materialItems.length > 0 && !incomingConnectionData?.matOutput?.material) {
        setFormData(prev => ({
          ...prev,
          matInput: {
            ...prev.matInput,
            material: materialItems[0].value,
            unit: materialItems[0].unit,
            smc: materialItems[0].smc
          }
        }));
        setMatInputAutoFillLabel(materialItems[0].label);
      }
    } catch (err) {
      console.error("Error processing autofill data:", err);
    }
  }, [open, autoFillInputs, currentStepIndex, incomingConnectionData]);

  // Reset the form to initial state
  const resetForm = () => {
    setCurrentStepIndex(0);
    setShouldCompleteForm(false);
    setFormData({
      activity: "",
      technology: "",
      startYear: "2000",
      endYear: "2073",
      customTechnology: "",
      customActivity: "",
      byproductTechnology: "", // Reset byproduct technology
      energyInputs: [], // Initialize empty arrays for multiple entries
      emissions: [],
      materialInputs: [],
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "GJ" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      byproductEnergy: [{ byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" }],
      byproductMat: [{ byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "Tonnes" }],
      financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCostUnit: "", omCost: "" },
      financialEntries: {}
    });
    setEnergyInputAutoFillLabel("");
    setMatInputAutoFillLabel("");
    setTechnologyAutoFillLabel(""); // Reset technology auto-fill label
    
    // Reset outputs and technologies
    setOutputs([]);
    setActiveOutputTab("");
    setTechnologies([technologiesName || "Technology 1"]);
    setActiveTechnology(technologiesName || "Technology 1");
    setAvailableTechnologies([]); // Reset available technologies

    // Reset technology-specific form data
    const resetTechFormData: FormData = {
      technology: "",
      activity: "",
      startYear: String(existingFormData?.details?.startYear || "2025"),
      endYear: String(existingFormData?.details?.endYear || "2022"),
      // Keep for compatibility
      customTechnology: "",
      customActivity: "",
      // Step 1: Inputs (now Step 3) - Arrays for multiple entries
      energyInputs: (existingFormData?.details?.inputs?.energies || []).map(
        (item: any, idx: number) => ({ id: item.id || `energy-${idx}`, ...item })
      ),
      emissions: (existingFormData?.details?.inputs?.emissions || []).map(
        (item: any, idx: number) => ({ id: item.id || `emission-${idx}`, factor: item.factor ?? item.ef ?? '', ...item })
      ),
      materialInputs: (existingFormData?.details?.inputs?.materials || []).map(
        (item: any, idx: number) => ({ id: item.id || `material-${idx}`, smc: item.smc ?? '', ...item })
      ),
      // Legacy single entries - kept for compatibility
      energyInput: existingFormData?.details?.outputs?.energies?.[0] || {
        source: "",
        unit: "GJ",
        cost: "",
        sec: ""
      },
      emission: existingFormData?.details?.outputs?.emissions?.[0] || {
        source: "",
        ef: "",
        unit: "GJ"
      },
      matInput: {
        ...(existingFormData?.details?.outputs?.materials?.[0] || {}),
        cost: existingFormData?.details?.outputs?.materials?.[0]?.cost ?? "",
        smc: existingFormData?.details?.outputs?.materials?.[0]?.smc ?? "",
        material: existingFormData?.details?.outputs?.materials?.[0]?.material ?? "",
        unit: existingFormData?.details?.outputs?.materials?.[0]?.unit ?? "Tonnes",
      },
      // Step 3: By-products (now Step 2)
      byproductTechnology: "", // Field for by-product technology
      byproductEnergy: existingFormData?.details?.byproducts?.energies || [{
        byproduct: "",
        unit: "GJ",
        bppo: "",
        connect: "",
        replaced: ""
      }],
      byproductMat: existingFormData?.details?.byproducts?.materials || [{
        byproduct: "",
        unit: "Tonnes",
        bppo: "",
        connect: "",
        replaced: "",
        techEmissionFactor: "",
        emissionFactor: "",
        emissionUnit: "Tonnes"
      }],
      // Step 4: Financial (remains Step 4)
      financial: {
        capacity: String(existingFormData?.details?.financial?.capacity ?? ""),
        capacityUnit: String(existingFormData?.details?.financial?.capacityUnit ?? "Tonnes/day"),
        // capitalCost: String(existingFormData?.details?.financial?.capitalCost ?? ""),
        capitalCostUnit: String(existingFormData?.details?.financial?.capitalCostUnit ?? ""),
        omCost: String(existingFormData?.details?.financial?.operatingMaintenanceCost ?? ""),
        // omCostUnit: String(existingFormData?.details?.financial?.operatingMaintenanceCostUnit ?? "")
      },
      financialEntries: {}
    };

    setTechnologyFormData({
      [technologiesName || "Technology 1"]: resetTechFormData
    });
  };

  // Handle form input changes
  const updateFormField = (section: string, field: string, value: string) => {
    // Check if this is a nested path for financialEntries
    if (section.startsWith('financialEntries.')) {
      const [, key, nestedField] = section.split('.');
      
      setFormData(prev => ({
        ...prev,
        financialEntries: {
          ...prev.financialEntries,
          [key]: {
            ...(prev.financialEntries?.[key] || { capacity: '', capacityUnit: 'Tonnes/day', capitalCostUnit: '', omCost: '' }),
            [nestedField || field]: value
          }
        }
      }));
    } else {
      // Regular section update
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      }));
    }
  };

  // Simple field update for top-level fields
  const updateField = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Add a new technology (replaces addNewOutput)
  const addNewTechnology = (customTechName?: string) => {
    const newTechName = customTechName || `Technology ${technologies.length + 1}`;

    // Only add if it doesn't already exist
    if (!technologies.includes(newTechName)) {
      setTechnologies([...technologies, newTechName]);
      setActiveTechnology(newTechName);

      // Initialize form data for the new technology
      const initialFormData: FormData = {
        technology: newTechName,
        activity: "",
        customTechnology: "",
        customActivity: "",
        // Arrays for multiple entries (actual form data)
        energyInputs: [],
        emissions: [],
        materialInputs: [],
        // Legacy single entries (for compatibility)
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        // By-products
        byproductTechnology: newTechName,
        byproductEnergy: [{ byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" }],
        byproductMat: [{ byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" }],
        energyByProducts: [],
        materialByProducts: [],
        // Financial
        financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCostUnit: "", omCost: "" },
        financialEntries: {}
      };

      setTechnologyFormData(prev => ({
        ...prev,
        [newTechName]: initialFormData
      }));

      // Also create a corresponding output for this technology
      const newOutputId = `output-${outputs.length}`;
      const initialEnergyOutput: EnergyOutput = {
        id: `energy-${Date.now()}-0`,
        energy: "",
        unit: "GJ",
        sec: "",
        final: false,
        connect: "",
        qty: "",
        qtyUnit: "GJ"
      };

      const initialMatOutput: MaterialOutput = {
        id: `material-${Date.now()}-0`,
        material: "",
        unit: "Tonnes",
        smc: "",
        final: false,
        connect: "",
        qty: "",
        qtyUnit: "Tonnes"
      };

      const newOutput: OutputForm = {
        id: newOutputId,
        targetNode: "",
        outputTechnology: newTechName,
        energyOutputs: [initialEnergyOutput],
        matOutputs: [initialMatOutput]
      };

      setOutputs([...outputs, newOutput]);
      setActiveOutputTab(newOutputId);
    } else {
      // If technology already exists, just switch to it
      setActiveTechnology(newTechName);
    }
  };

  // Get form data for current technology
  const getCurrentTechnologyFormData = (): FormData => {
    if (technologyFormData[activeTechnology]) {
      return technologyFormData[activeTechnology];
    }

    // Fallback: create initial form data for the technology
    const fallbackFormData: FormData = {
      technology: activeTechnology,
      activity: "",
      startYear: "2000",
      endYear: "2023",
      customTechnology: "",
      customActivity: "",
      // Arrays for multiple entries (actual form data)
      energyInputs: [],
      emissions: [],
      materialInputs: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: activeTechnology,
      byproductEnergy: [{ byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" }],
      byproductMat: [{ byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" }],
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "", capacityUnit: "Tonnes/day", capitalCostUnit: "", omCost: "" },
      financialEntries: {}
    };

    return fallbackFormData;
  };

  // Update form data for current technology
  const updateTechnologyFormData = (field: string, value: string | any[]) => {
    setTechnologyFormData(prev => {
      // Ensure we have form data for the current technology
      const currentTechData = prev[activeTechnology] || getCurrentTechnologyFormData();

      return {
        ...prev,
        [activeTechnology]: {
          ...currentTechData,
          [field]: value
        }
      };
    });

    // Also update global formData for backward compatibility
    if (typeof value === 'string') {
      updateField(field, value);
    }
  };

  // Update form field for current technology (for nested objects)
  const updateTechnologyFormField = (section: string, field: string, value: string) => {
    setTechnologyFormData(prev => {
      // Ensure we have form data for the current technology
      const currentTechData = prev[activeTechnology] || getCurrentTechnologyFormData();

      return {
        ...prev,
        [activeTechnology]: {
          ...currentTechData,
          [section]: {
            ...currentTechData[section],
            [field]: value
          }
        }
      };
    });

    // Also update global formData for backward compatibility
    updateFormField(section, field, value);
  };

  // Update technology name when technology is selected in dropdown
  const updateTechnologyName = (oldTechName: string, newTechName: string) => {
    if (oldTechName !== newTechName) {
      setTechnologies(prev => prev.map(tech => tech === oldTechName ? newTechName : tech));
      setActiveTechnology(newTechName);

      // Move form data from old technology name to new technology name
      setTechnologyFormData(prev => {
        const newData = { ...prev };
        if (newData[oldTechName]) {
          newData[newTechName] = { ...newData[oldTechName] };
          delete newData[oldTechName];
        }
        return newData;
      });

      // Also update the corresponding output
      setOutputs(prev => prev.map(output =>
        output.outputTechnology === oldTechName
          ? { ...output, outputTechnology: newTechName }
          : output
      ));
    }
  };

  // Handle output field updates including technology synchronization
  const updateOutputField = (outputId: string, fieldPath: string, value: any) => {

    setOutputs(prevOutputs => {
      const newOutputs = [...prevOutputs];
      const outputIndex = newOutputs.findIndex(output => output.id === outputId);

      if (outputIndex !== -1) {
        // Handle nested paths like 'energyOutput.energy'
        if (fieldPath.includes('.')) {
          const [section, field] = fieldPath.split('.');
          newOutputs[outputIndex] = {
            ...newOutputs[outputIndex],
            [section]: {
              ...newOutputs[outputIndex][section],
              [field]: value
            }
          };
        } else {
          // Handle direct properties like 'outputTechnology', 'energyOutputs', 'matOutputs'
          newOutputs[outputIndex] = {
            ...newOutputs[outputIndex],
            [fieldPath]: value
          };

          // Synchronize technology selection with byproduct immediately
          if (fieldPath === 'outputTechnology' && value) {
            setFormData(prev => ({
              ...prev,
              byproductTechnology: value
            }));
            setTechnologyAutoFillLabel(`${value} (synchronized from output)`);
          }
        }
      }

      return newOutputs;
    });
  };

  // Function to clear specific validation errors
  const clearError = (errorKey: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[errorKey];
      return newErrors;
    });
  };

  // Navigation: Next step
  const handleNext = () => {
    // No validation or required checks
    if (currentStepIndex === SUPPLIER_TECH_STEPS.length - 1) {
      setShouldCompleteForm(true);
      handleFinalSubmit();
    } else {
      setCurrentStepIndex(prevIndex => prevIndex + 1);
    }
  };

  // Navigation: Previous step
  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prevIndex => prevIndex - 1);
    }
  };

  // New function for final submission after completing all steps
  const handleFinalSubmit = () => {
    try {
      // Build success toast message
      const sourceLabel = sourceNode?.data?.label || "Node";
      // Get all connections from form data
      const connections = processConnectionsFromFormData(
        { ...formData, outputs },
        sourceNode?.id || "",
        []
      );
      // Count total valid connections
      const validConnectionCount = connections.length;
      // Prepare final form data to submit
      const allEnergyInputs = [];
      const allMaterialInputs = [];
      const allEmissions = [];
      Object.values(technologyFormData).forEach((techData) => {
        if (techData && typeof techData === 'object') {
          const formData = techData as FormData;
          if (formData.energyInputs) allEnergyInputs.push(...formData.energyInputs);
          if (formData.materialInputs) allMaterialInputs.push(...formData.materialInputs);
          if (formData.emissions) allEmissions.push(...formData.emissions);
        }
      });
      const finalFormData = {
        ...formData,
        outputs,
        energyInputs: allEnergyInputs,
        materialInputs: allMaterialInputs,
        emissions: allEmissions,
        technologyFormData,
        technologies,
        activeTechnology,
        formCompleted: true,
        connectionCount: validConnectionCount,
        processedConnections: connections
      };
      if (formData.customTechnology && formData.customActivity) {
        finalFormData.technology = formData.customTechnology;
        finalFormData.activity = formData.customActivity;
      }
      const connectionMessage = validConnectionCount > 0
        ? `${validConnectionCount} connection${validConnectionCount !== 1 ? 's' : ''} created from ${sourceLabel}`
        : `Activity created: ${sourceLabel} (no connections)`;
      toast({ 
        title: "Connection completed", 
        description: connectionMessage
      });
      onComplete(finalFormData);
      resetForm();
    } catch (error) {
      toast({ 
        title: "Connection error", 
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });
      setShouldCompleteForm(false);
    }
  };

  // Regular form submission handler (converted to be only for DOM events)
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (currentStepIndex === SUPPLIER_TECH_STEPS.length - 1) {
      setShouldCompleteForm(true);
      handleFinalSubmit();
    } else {
      handleNext();
    }
  };

  // Render content based on current step
  const renderStepContent = (stepId: StepId) => {
    
    switch (stepId) {
      case "outputs":
        return (
          <OutputsStep
            outputs={outputs}
            setOutputs={setOutputs}
            activeOutputTab={activeOutputTab}
            setActiveOutputTab={setActiveOutputTab}
            errors={errors}
            availableNodes={availableNodes}
            updateOutputField={(fieldPath, value) => {

              // Find the output that corresponds to the current technology
              const currentTechOutput = outputs.find(o => o.outputTechnology === activeTechnology);

              if (currentTechOutput) {
                updateOutputField(currentTechOutput.id, fieldPath, value);
              } else {
                console.log(`No current tech output found for technology:`, activeTechnology);
              }
            }}
            clearError={clearError}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            onAddTechnology={addNewTechnology}
            apiTechnologies={apiData.technologies.technologies}
            apiMaterials={apiData.materials.materials}
            apiEnergies={apiData.energies.energies}
            apiEmissions={apiData.emissions.emissions}
            isLoadingApiData={apiData.isLoading}
          />
        );

      case "byproducts":
        return (
          <ByProductsStep
            formData={formData}
            updateFormField={updateTechnologyFormField}
            errors={errors}
            availableNodes={availableNodes}
            technologyAutoFillLabel={technologyAutoFillLabel}
            availableTechnologies={availableTechnologies}
            updateField={updateTechnologyFormData}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            onAddTechnology={addNewTechnology}
            apiTechnologies={apiData.technologies.technologies}
            apiMaterials={apiData.materials.materials}
            apiEnergies={apiData.energies.energies}
            apiEmissions={apiData.emissions.emissions}
            isLoadingApiData={apiData.isLoading}
          />
        );

      case "inputs":
        return (
          <InputsStep
            formData={formData}
            updateField={updateTechnologyFormData}
            updateFormField={updateTechnologyFormField}
            errors={errors}
            usingManualEntry={false}
            energyInputAutoFillLabel={energyInputAutoFillLabel}
            matInputAutoFillLabel={matInputAutoFillLabel}
            technologyAutoFillLabel={technologyAutoFillLabel}
            availableTechnologies={availableTechnologies}
            availableNodes={availableNodes}
            technologies={technologies}
            activeTechnology={activeTechnology}
            setActiveTechnology={setActiveTechnology}
            // onAddTechnology={addNewTechnology}
            updateTechnologyName={updateTechnologyName}
            apiTechnologies={apiData.technologies.technologies}
            apiMaterials={apiData.materials.materials}
            apiEnergies={apiData.energies.energies}
            apiEmissions={apiData.emissions.emissions}
            isLoadingApiData={apiData.isLoading}
            onCreateTechnology={apiData.technologies.addTechnology}
          />
        );

      case "financial":
        return (
          <FinancialStep
            formData={formData}
            updateFormField={updateTechnologyFormField}
            errors={errors}
            outputs={outputs}
            availableNodes={availableNodes}
            technologies={technologies}
          />
        );

      default:
        return <p>Unknown step</p>;
    }
  };

  return (
    <Dialog 
      open={open} 
      onOpenChange={(isOpen) => {
        if (!isOpen) {
              onClose();
        //   if (shouldCompleteForm) {
        //     onClose();
        //   } else {
        //     if (currentStepIndex > 0) {
        //       if (confirm("Are you sure you want to close? Your changes will be lost.")) {
        //         onClose();
        //       }
        //     } else {
        //       onClose();
        //     }
        //   }
        }
      }}
    >
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>
            {supplierCompanyName}
          </DialogTitle>
          <DialogClose asChild>
            <button
              type="button"
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
              aria-label="Close"
            >
              ×
            </button>
          </DialogClose>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Step indicators */}
          <StepNavigation steps={SUPPLIER_TECH_STEPS} currentStepIndex={currentStepIndex} />

          {/* Current step content */}
          <div className="min-h-[400px] max-h-[60vh] overflow-y-auto px-1">
            {renderStepContent(SUPPLIER_TECH_STEPS[currentStepIndex].id)}
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={currentStepIndex === 0}
            >
              Back
            </Button>
            
            {currentStepIndex < SUPPLIER_TECH_STEPS.length - 1 ? (
              <Button 
                type="button"
                onClick={handleNext}
              >
                Next
              </Button>
            ) : ( null
              // <Button type="button" onClick={handleFinalSubmit}>
              //   Complete
              // </Button>
            )}
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
