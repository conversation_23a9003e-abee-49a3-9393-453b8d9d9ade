import React from 'react';
import { Edge<PERSON><PERSON>, getSmooth<PERSON>tep<PERSON><PERSON>, EdgeLabel<PERSON><PERSON><PERSON>, BaseEdge } from '@xyflow/react';

// Define the data interface for multi-edge data
interface MultiEdgeData {
  multiEdgeOffset?: number;
  multiEdgeTotal?: number;
  multiEdgeIndex?: number;
  type?: string;
}

/**
 * Calculate a custom curved path for multiple edges to ensure visual separation
 */
const calculateCustomPath = (
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  sourcePosition: string,
  targetPosition: string,
  offset: number,
  isHorizontal: boolean
): string => {
  // Calculate the distance between nodes
  const dx = targetX - sourceX;
  const dy = targetY - sourceY;
  const distance = Math.sqrt(dx * dx + dy * dy);
  
  // Create control points for a smooth curve
  let controlPoint1X, controlPoint1Y, controlPoint2X, controlPoint2Y;
  
  if (isHorizontal) {
    // For horizontal edges, create vertical offset
    const midX = sourceX + dx / 2;
    controlPoint1X = sourceX + dx / 3;
    controlPoint1Y = sourceY + offset;
    controlPoint2X = sourceX + (2 * dx) / 3;
    controlPoint2Y = targetY + offset;
  } else {
    // For vertical edges, create horizontal offset
    const midY = sourceY + dy / 2;
    controlPoint1X = sourceX + offset;
    controlPoint1Y = sourceY + dy / 3;
    controlPoint2X = targetX + offset;
    controlPoint2Y = sourceY + (2 * dy) / 3;
  }
  
  // Create a smooth cubic bezier curve
  return `M ${sourceX} ${sourceY} C ${controlPoint1X} ${controlPoint1Y}, ${controlPoint2X} ${controlPoint2Y}, ${targetX} ${targetY}`;
};

/**
 * Custom edge component that handles multiple edges between same nodes
 * with proper spacing and visual differentiation
 */
export const MultiEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  label,
  labelStyle,
  selected
}) => {
  // Get multi-edge data with proper typing
  const multiEdgeData = data as MultiEdgeData;
  const multiEdgeOffset = multiEdgeData?.multiEdgeOffset || 0;
  const multiEdgeTotal = multiEdgeData?.multiEdgeTotal || 1;
  const multiEdgeIndex = multiEdgeData?.multiEdgeIndex || 0;

  if (multiEdgeTotal > 1) {
    console.log(`🎨 Rendering multi-edge ${multiEdgeIndex + 1}/${multiEdgeTotal}:`, {
      id,
      label,
      offset: multiEdgeOffset,
      sourceX,
      sourceY,
      targetX,
      targetY
    });
  }
  
  // Calculate visual separation based on edge direction and position
  const isHorizontal = Math.abs(targetX - sourceX) > Math.abs(targetY - sourceY);
  
  // Use larger offset for visual separation
  const visualOffset = multiEdgeTotal > 1 ? multiEdgeOffset * 3 : 0;
  
  let edgePath: string;
  let labelX: number;
  let labelY: number;
  
  if (multiEdgeTotal > 1) {
    // Use custom path calculation for multiple edges
    edgePath = calculateCustomPath(
      sourceX,
      sourceY,
      targetX,
      targetY,
      sourcePosition,
      targetPosition,
      visualOffset,
      isHorizontal
    );
    
    // Calculate label position along the custom path
    const dx = targetX - sourceX;
    const dy = targetY - sourceY;
    labelX = sourceX + dx / 2;
    labelY = sourceY + dy / 2 + visualOffset * 0.5;
  } else {
    // Use default smooth step path for single edges
    [edgePath, labelX, labelY] = getSmoothStepPath({
      sourceX: sourceX,
      sourceY: sourceY,
      sourcePosition,
      targetX: targetX,
      targetY: targetY,
      targetPosition,
      borderRadius: 8
    });
  }
  
  // Enhanced styling for multiple edges
  const edgeStyle = {
    ...style,
    // Only apply dotted pattern if the edge is actually a byproduct
    // Otherwise, respect the original style (solid for outputs, dotted for byproducts)
    ...(multiEdgeTotal > 1 && {
      // Always make byproducts dotted, regardless of position in multi-edge group
      ...(multiEdgeData?.type?.includes('byproduct') && {
        strokeDasharray: '5,5' // Always dotted for byproducts
      }),
      animation: multiEdgeData?.type?.includes('byproduct') ? 'dash 2s linear infinite' : 'none'
    })
  };
  
  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        style={edgeStyle}
        markerEnd={markerEnd}
      />
      {label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              ...labelStyle,
              pointerEvents: 'all',
              // Offset labels for multiple edges to prevent overlap
              marginTop: visualOffset * 0.3,
              zIndex: 1000 + multiEdgeIndex
            }}
            className="nodrag nopan"
          >
            {label}
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

/**
 * Enhanced smooth step edge with better multiple edge support
 */
export const EnhancedSmoothStepEdge: React.FC<EdgeProps> = (props) => {
  const { data } = props;
  const multiEdgeData = data as MultiEdgeData;
  
  // Use custom multi-edge component if this edge is part of multiple edges
  if (multiEdgeData?.multiEdgeTotal && multiEdgeData.multiEdgeTotal > 1) {
    return <MultiEdge {...props} />;
  }
  
  // Use default smooth step path for single edges
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX: props.sourceX,
    sourceY: props.sourceY,
    sourcePosition: props.sourcePosition,
    targetX: props.targetX,
    targetY: props.targetY,
    targetPosition: props.targetPosition,
    borderRadius: 8
  });
  
  return (
    <>
      <BaseEdge
        id={props.id}
        path={edgePath}
        style={props.style}
        markerEnd={props.markerEnd}
      />
      {props.label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              ...props.labelStyle,
              pointerEvents: 'all'
            }}
            className="nodrag nopan"
          >
            {props.label}
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

// Export edge types for React Flow
export const customEdgeTypes = {
  'smoothstep': EnhancedSmoothStepEdge,
  'multi-edge': MultiEdge
};
