import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Index from '@/pages/Index';
import Dashboard from '@/pages/Dashboard';
import DocumentChat from '@/pages/DocumentChat';
import QuerySearch from '@/pages/QuerySearch';
import Login from '@/pages/Login';
import SignUp from '@/pages/SignUp';
import Profile from '@/pages/Profile';
import DomainCorpus from '@/pages/DomainCorpus';
import Assessment from '@/pages/Assessment';
import ClimateRiskAssessment from '@/pages/ClimateRiskAssessment';
import NotFound from '@/pages/NotFound';
import TopicDetail from '@/pages/TopicDetail';
import { ThemeProvider } from '@/components/ThemeProvider';
import { ToastProvider } from '@/contexts/ToastContext';
import { Toaster } from '@/components/ui/toaster';
import NotesCreator from '@/pages/NotesCreator';
import Optimizer from '@/pages/Optimizer';
import IndustryFlow from '@/pages/IndustryFlow';
import Layout from '@/components/Layout';
import Marketplace from '@/pages/Marketplace';
import ActivityDetails from '@/pages/ActivityDetails';
import TechDetails from '@/pages/TechDetails';
import { SectorActivitiesProvider } from '@/contexts/SectorActivitiesContext';

// Shared layout routes
function LayoutRoute({ element }: { element: React.ReactNode }) {
  return (
    <Layout>
      {element}
    </Layout>
  );
}

function App() {
  return (
    <div className="App">
      <ThemeProvider>
        <ToastProvider>
          <SectorActivitiesProvider>
            <Toaster />
            <BrowserRouter>
              <Routes>
                {/* Auth and special-case routes */}
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<SignUp />} />
                
                {/* Main routes with shared layout */}
                <Route path="/" element={<Navigate to="/dashboard" replace={true} />} />
                <Route path="/home" element={<Navigate to="/dashboard" replace={true} />} />
                {/* <Route path="/" element={<Layout><Index /></Layout>} />
                <Route path="/home" element={<Layout><Index /></Layout>} /> */}
                <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
                <Route path="/dashboard/:tab/:tileId" element={<TopicDetail />} />
                <Route path="/document-chat" element={<Layout><DocumentChat /></Layout>} />
                <Route path="/query-search" element={<Layout><QuerySearch /></Layout>} />
                <Route path="/notes-creator" element={<Layout><NotesCreator /></Layout>} />
                <Route path="/optimizer" element={<Layout><Optimizer /></Layout>} />
                <Route path="/industry-flow/:industryId" element={<Layout><IndustryFlow /></Layout>} />
                <Route path="/profile" element={<Layout><Profile /></Layout>} />
                <Route path="/domain-corpus" element={<Layout><DomainCorpus /></Layout>} />
                <Route path="/assessment" element={<Layout><Assessment /></Layout>} />
                <Route path="/climate-risk-assessment" element={<Layout><ClimateRiskAssessment /></Layout>} />
                <Route path="/marketplace" element={<Layout><Marketplace /></Layout>} />
                <Route path="/marketplace/:activityName" element={<Layout><ActivityDetails /></Layout>} />
                <Route path="/marketplace/:activityId/:techSlug" element={<Layout><TechDetails /></Layout>} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </SectorActivitiesProvider>
        </ToastProvider>
      </ThemeProvider>
    </div>
  );
}

export default App;
