// Common API utilities for the app
import { API_BASE_URL } from "@/utils/endPoints";

export function getAuthHeaders() {
  const accessToken = localStorage.getItem('accessToken');
  if (!accessToken) {
    throw new Error('No access token found. Please log in again.');
  }
  return {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  };
}

export async function fetchRegions() {
  const res = await fetch(`${API_BASE_URL}/regions`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  if (!res.ok) throw new Error('Failed to fetch regions');
  return await res.json();
}

export async function fetchExperts() {
  const res = await fetch(`${API_BASE_URL}/experts`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  if (!res.ok) throw new Error('Failed to fetch experts');
  return await res.json();
}

export async function fetchExpertsByActivity(activityName: string) {
  const res = await fetch(`${API_BASE_URL}/experts/by-activity/${activityName}`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  if (!res.ok) throw new Error('Failed to fetch experts for this activity');
  return await res.json();
} 