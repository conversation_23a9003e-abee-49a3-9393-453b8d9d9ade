
import { useToast } from "@/hooks/use-toast";
import { API_BASE_URL } from "@/utils/endPoints";

interface TopicSummaryRequest {
  query: string;
  search_type: "global";
  technology_sector: string;
  question_type: "Default";
}

interface TopicChatRequest {
  query: string;
  search_type: "global";
  technology_sector: string;
  question_type: "Default";
}

interface ToastUtils {
  toast: ReturnType<typeof useToast>["toast"];
  dismiss: ReturnType<typeof useToast>["dismiss"];
  toasts: ReturnType<typeof useToast>["toasts"];
}

export const fetchTopicSummary = async (
  topicTitle: string,
  sector: string,
  toastUtils: ToastUtils
): Promise<string | null> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }

    const requestPayload: TopicSummaryRequest = {
      query: `Provide a comprehensive, well-structured explanation of the following topic:\n\n"${topicTitle}"\n\nin the context of the "${sector}" industry. Cover:\n- Its relevance and implications for the sector\n- Any recent developments, context, or key issues\n- Why it matters for companies in this space\n\nResponse must be 150–250 words, grounded in corpus data, without generic or unrelated info.\n\nReturn plain text only.`,
      search_type: "global",
      technology_sector: sector,
      question_type: "Default"
    };

    console.log("Fetching topic summary:", JSON.stringify(requestPayload, null, 2));

    const response = await fetch(API_BASE_URL+'/ask', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestPayload)
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch topic summary: ${response.status}`);
    }

    const data = await response.json();
    console.log("Topic summary response:", data);

    if (typeof data.response === 'string') {
      return data.response;
    } else if (typeof data.response === 'object' && data.response.content) {
      return data.response.content;
    }

    return data.response || null;
  } catch (error: any) {
    console.error("Topic summary fetch error:", error);
    toastUtils.toast({
      title: "Summary Fetch Error",
      description: error.message || "Failed to fetch topic summary",
      variant: "destructive"
    });
    return null;
  }
};

export const fetchTopicChatResponse = async (
  question: string,
  sector: string,
  toastUtils: ToastUtils
): Promise<string | null> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }

    const requestPayload: TopicChatRequest = {
      query: question,
      search_type: "global",
      technology_sector: sector,
      question_type: "Default"
    };

    console.log("Fetching chat response:", JSON.stringify(requestPayload, null, 2));

    const response = await fetch(API_BASE_URL+'/ask', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestPayload)
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch chat response: ${response.status}`);
    }

    const data = await response.json();
    console.log("Chat response:", data);

    if (typeof data.response === 'string') {
      return data.response;
    } else if (typeof data.response === 'object' && data.response.content) {
      return data.response.content;
    }

    return data.response || null;
  } catch (error: any) {
    console.error("Chat response fetch error:", error);
    toastUtils.toast({
      title: "Chat Error",
      description: error.message || "Failed to get response",
      variant: "destructive"
    });
    return null;
  }
};
