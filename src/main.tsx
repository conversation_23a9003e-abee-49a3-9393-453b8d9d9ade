
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import './utils/optimizerApiTest' // Import test utility for development

// Add styles for the connection handles
const styleElement = document.createElement('style');
styleElement.textContent = `
  .connection-handle:hover {
    transform: translateY(-50%) scale(1.3) !important;
    background: #f0ebff !important;
    cursor: crosshair;
    box-shadow: 0 4px 8px rgba(155, 135, 245, 0.4) !important;
  }
  
  .react-flow__handle:hover {
    transform: scale(1.3) !important;
    background: #f0ebff !important;
    cursor: crosshair;
    box-shadow: 0 4px 8px rgba(155, 135, 245, 0.4) !important;
  }
  
  /* For handles on bottom and top that use translateX instead of translateY */
  .react-flow__handle[data-handlepos="bottom"]:hover,
  .react-flow__handle[data-handlepos="top"]:hover {
    transform: translateX(-50%) scale(1.3) !important;
  }
`;

document.head.appendChild(styleElement);

createRoot(document.getElementById("root")!).render(<App />);
