// Removed hardcoded default flow template nodes and edges
// All flows now use the unified rendering system

// Removed hardcoded default flow template edges and virtual input nodes
// All flows now use the unified rendering system which creates edges dynamically

// Edge styling constants used by unified rendering system
export const edgeLabelStyle = {
  fill: '#787882',
  fontWeight: 400,
  fontSize: 13.5,
  userSelect: "none",
  background: "rgba(255,255,255,0.7)",
  padding: "2px 5px",
  borderRadius: "3px",
  border: "1px solid #ded3fd",
};

export const edgeStyle = {
  stroke: '#9b87f5',
  strokeWidth: 2,
  opacity: 0.85,
};

export const flowTypes = [
  { id: 'main', label: 'Main flow', color: 'bg-[#C7C2E4] text-[#684dc4]' },
  { id: 'sub', label: 'Sub flow', color: 'bg-[#e0ffe0] text-[#18cd22]' },
  { id: 'byproduct', label: 'By-product flow', color: 'bg-[#fcfdb8] text-[#e0be0b]' },
];

export const nodeDefaultStyle = {
  borderRadius: '14px',
  background: 'transparent',
  border: '2px solid #9b87f5',
  color: '#222',
  minWidth: 180,
  minHeight: 60,
  fontWeight: 500,
  padding: '16px 26px',
  fontSize: 16,
  boxShadow: '0 4px 24px 0 rgba(126,105,171,0.10)',
};

export const VERTICAL_GAP = 60 + 70;
