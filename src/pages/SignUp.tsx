import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, Check, Moon, Sun } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useTheme } from '@/components/ThemeProvider';
import Logo from '@/components/Logo';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal
} from '@/components/ui/dropdown-menu';
import { API_BASE_URL } from '@/utils/endPoints';

const industries = [
  "Manufacturing",
  "Supply Chain",
  "Automotive",
  "Banking & Finance",
  "Energy"
];

const manufacturingSectors = [
  "Cement",
  "Steel",
  "Oil & Gas",
  "Chlor-Alkali",
  "Textile",
  "Aluminium",
  "Fertilizer",
  "Power",
  "Pulp & Paper"
];

const functions = [
  "Sustainability",
  "Risk",
  "Finance",
  "Procurement",
  "Operations",
  "Strategy",
  "R&D",
  "Legal & Compliance"
];

const countryCodes = [
  { code: "+91", country: "India" },
  { code: "+1", country: "USA" },
  { code: "+44", country: "UK" },
  { code: "+49", country: "Germany" },
  { code: "+33", country: "France" },
  { code: "+81", country: "Japan" },
  { code: "+86", country: "China" },
  { code: "+61", country: "Australia" },
  { code: "+65", country: "Singapore" },
  { code: "+971", country: "UAE" }
];

// Validation functions
const validateName = (name: string): { isValid: boolean; error?: string } => {
  if (!name.trim()) {
    return { isValid: false, error: "Name is required" };
  }
  if (name.length < 2) {
    return { isValid: false, error: "Name must be at least 2 characters long" };
  }
  if (name.length > 50) {
    return { isValid: false, error: "Name must not exceed 50 characters" };
  }
  // Allow letters, spaces, hyphens, apostrophes, and dots (for titles like Jr., Sr.)
  const nameRegex = /^[a-zA-Z\s\-'\.]+$/;
  if (!nameRegex.test(name)) {
    return { isValid: false, error: "Name can only contain letters, spaces, hyphens, apostrophes, and dots" };
  }
  // Prevent excessive spaces or special characters
  if (/\s{2,}/.test(name) || /[-'\.]{2,}/.test(name)) {
    return { isValid: false, error: "Name contains invalid character sequences" };
  }
  return { isValid: true };
};

const validateCompany = (company: string): { isValid: boolean; error?: string } => {
  if (!company.trim()) {
    return { isValid: false, error: "Company name is required" };
  }
  if (company.length < 2) {
    return { isValid: false, error: "Company name must be at least 2 characters long" };
  }
  if (company.length > 100) {
    return { isValid: false, error: "Company name must not exceed 100 characters" };
  }
  // Allow letters, numbers, spaces, common business symbols
  const companyRegex = /^[a-zA-Z0-9\s\-'&\.\,\(\)]+$/;
  if (!companyRegex.test(company)) {
    return { isValid: false, error: "Company name contains invalid characters" };
  }
  // Prevent excessive spaces or special characters
  if (/\s{2,}/.test(company) || /[-'&\.\,\(\)]{3,}/.test(company)) {
    return { isValid: false, error: "Company name contains invalid character sequences" };
  }
  return { isValid: true };
};

const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  if (!email.trim()) {
    return { isValid: false, error: "Email is required" };
  }
  if (email.length > 254) {
    return { isValid: false, error: "Email address is too long" };
  }
  // Enhanced email regex that prevents common injection patterns
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, error: "Please enter a valid email address" };
  }
  // Prevent suspicious patterns
  if (email.includes('..') || email.includes('@@') || email.startsWith('.') || email.endsWith('.')) {
    return { isValid: false, error: "Email format is invalid" };
  }
  // Check for business email (optional warning, not blocking)
  const personalDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com'];
  const domain = email.split('@')[1]?.toLowerCase();
  if (personalDomains.includes(domain)) {
    // This is just a warning, not a blocking validation
    console.warn('Personal email domain detected');
  }
  return { isValid: true };
};

const validatePassword = (password: string): { isValid: boolean; error?: string } => {
  if (!password) {
    return { isValid: false, error: "Password is required" };
  }
  if (password.length < 8) {
    return { isValid: false, error: "Password must be at least 8 characters long" };
  }
  if (password.length > 128) {
    return { isValid: false, error: "Password must not exceed 128 characters" };
  }
  // Check for at least one uppercase, one lowercase, one number, and one special character
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

  if (!hasUppercase) {
    return { isValid: false, error: "Password must contain at least one uppercase letter" };
  }
  if (!hasLowercase) {
    return { isValid: false, error: "Password must contain at least one lowercase letter" };
  }
  if (!hasNumber) {
    return { isValid: false, error: "Password must contain at least one number" };
  }
  if (!hasSpecialChar) {
    return { isValid: false, error: "Password must contain at least one special character" };
  }

  return { isValid: true };
};

const validatePhone = (phone: string, countryCode: string): { isValid: boolean; error?: string } => {
  if (!phone.trim()) {
    return { isValid: true }; // Phone is optional
  }

  // Remove spaces, hyphens, parentheses for validation
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');

  // Basic length validation based on country
  let minLength = 7;
  let maxLength = 15;

  switch (countryCode) {
    case "+91": // India
      minLength = 10;
      maxLength = 10;
      if (!/^[6-9]\d{9}$/.test(cleanPhone)) {
        return { isValid: false, error: "Indian mobile number must be 10 digits starting with 6-9" };
      }
      break;
    case "+1": // USA/Canada
      minLength = 10;
      maxLength = 10;
      if (!/^\d{10}$/.test(cleanPhone)) {
        return { isValid: false, error: "US/Canada number must be 10 digits" };
      }
      break;
    case "+44": // UK
      minLength = 10;
      maxLength = 11;
      break;
    default:
      // General validation for other countries
      if (!/^\d+$/.test(cleanPhone)) {
        return { isValid: false, error: "Phone number can only contain digits" };
      }
  }

  if (cleanPhone.length < minLength || cleanPhone.length > maxLength) {
    return { isValid: false, error: `Phone number must be between ${minLength} and ${maxLength} digits` };
  }

  return { isValid: true };
};

// Custom theme toggle component for signup page
const SignupThemeToggle: React.FC<{ theme: 'dark' | 'light'; onToggle: () => void }> = ({ theme, onToggle }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="rounded-full"
            aria-label="Toggle theme"
          >
            {theme === 'dark' ? (
              <Sun className="h-5 w-5 text-recrea-turquoise" />
            ) : (
              <Moon className="h-5 w-5 text-recrea-teal" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Switch to {theme === 'dark' ? 'light' : 'dark'} mode</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

const SignUp = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    company: '',
    email: '',
    phone: '',
    function: '',
    password: '',
    confirmPassword: '',
  });
  const [industry, setIndustry] = useState('');
  const [sector, setSector] = useState('');
  const [showIndustryDropdown, setShowIndustryDropdown] = useState(false);
  const [showFunctionDropdown, setShowFunctionDropdown] = useState(false);
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(countryCodes[0]);
  const [objectives, setObjectives] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({});
  const { toast } = useToast();
  const navigate = useNavigate();
  const { forceRefreshTheme } = useTheme(); // For theme context

  // Local theme state for signup page - initialize with stored value or default to dark
  const [localTheme, setLocalTheme] = useState<'dark' | 'light'>(() => {
    // Initialize with stored preference or default to dark for signup
    const signupTheme = localStorage.getItem('recrea-signup-theme') as 'dark' | 'light' | null;
    return signupTheme || 'dark';
  });

  // Flag to prevent cleanup when navigating
  const [isNavigating, setIsNavigating] = useState(false);

  // Override global theme for signup page
  useEffect(() => {
    // Store original global theme
    const originalGlobalTheme = localStorage.getItem('recrea-theme');

    // Set signup theme in localStorage if not already set
    const signupTheme = localStorage.getItem('recrea-signup-theme');
    if (!signupTheme) {
      localStorage.setItem('recrea-signup-theme', 'dark');
    }

    // Temporarily override the global theme in localStorage to match our local theme
    // This will make the global ThemeProvider use our desired theme
    localStorage.setItem('recrea-theme', localTheme);

    // Apply the theme to document
    document.documentElement.classList.remove('dark', 'light');
    document.documentElement.classList.add(localTheme);

    // Cleanup function to restore original global theme
    return () => {
      // Don't restore theme if we're navigating to login
      if (isNavigating) {
        return;
      }

      if (originalGlobalTheme) {
        localStorage.setItem('recrea-theme', originalGlobalTheme);
        document.documentElement.classList.remove('dark', 'light');
        document.documentElement.classList.add(originalGlobalTheme);
      } else {
        localStorage.setItem('recrea-theme', 'light');
        document.documentElement.classList.remove('dark', 'light');
        document.documentElement.classList.add('light');
      }
    };
  }, [localTheme]);

  // Custom toggle function for signup page
  const toggleSignupTheme = () => {
    const newTheme = localTheme === 'dark' ? 'light' : 'dark';
    setLocalTheme(newTheme);
    localStorage.setItem('recrea-signup-theme', newTheme);
  };

  // Function to handle navigation to login with proper theme reset
  const navigateToLogin = () => {
    // Set flag to prevent cleanup from interfering
    setIsNavigating(true);

    // Clean up signup-specific theme override
    // Remove the signup theme from localStorage
    localStorage.removeItem('recrea-signup-theme');

    // Reset to light theme for login page
    localStorage.setItem('recrea-theme', 'light');
    document.documentElement.classList.remove('dark', 'light');
    document.documentElement.classList.add('light');

    // Force the ThemeProvider to refresh its state from localStorage
    forceRefreshTheme();

    // Navigate immediately - the theme provider will handle the state correctly
    navigate('/login');
  };

  useEffect(() => {
    if (industry === 'Manufacturing' && (!sector || !manufacturingSectors.includes(sector))) {
      setSector(manufacturingSectors[0]);
    }
    if (industry !== 'Manufacturing') {
      setSector('');
    }
  }, [industry]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown-container')) {
        setShowFunctionDropdown(false);
        setShowIndustryDropdown(false);
        setShowCountryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Real-time validation for certain fields
    if (name === 'email' && value) {
      const validation = validateEmail(value);
      if (!validation.isValid) {
        setFieldErrors(prev => ({ ...prev, [name]: validation.error || '' }));
      }
    }

    if (name === 'fullName' && value) {
      const validation = validateName(value);
      if (!validation.isValid) {
        setFieldErrors(prev => ({ ...prev, [name]: validation.error || '' }));
      }
    }

    if (name === 'company' && value) {
      const validation = validateCompany(value);
      if (!validation.isValid) {
        setFieldErrors(prev => ({ ...prev, [name]: validation.error || '' }));
      }
    }

    if (name === 'phone' && value) {
      const validation = validatePhone(value, selectedCountry.code);
      if (!validation.isValid) {
        setFieldErrors(prev => ({ ...prev, [name]: validation.error || '' }));
      }
    }

    if (name === 'password' && value) {
      const validation = validatePassword(value);
      if (!validation.isValid) {
        setFieldErrors(prev => ({ ...prev, [name]: validation.error || '' }));
      }

      // Also validate confirm password if it exists
      if (formData.confirmPassword) {
        if (value !== formData.confirmPassword) {
          setFieldErrors(prev => ({ ...prev, confirmPassword: 'Passwords do not match' }));
        } else {
          setFieldErrors(prev => ({ ...prev, confirmPassword: '' }));
        }
      }
    }

    if (name === 'confirmPassword' && value) {
      if (value !== formData.password) {
        setFieldErrors(prev => ({ ...prev, [name]: 'Passwords do not match' }));
      } else {
        setFieldErrors(prev => ({ ...prev, [name]: '' }));
      }
    }
  };

  const handleObjectiveToggle = (objective: string) => {
    setObjectives(prev =>
      prev.includes(objective)
        ? prev.filter(o => o !== objective)
        : [...prev, objective]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFieldErrors({});

    // Comprehensive validation
    const errors: {[key: string]: string} = {};

    // Validate all required fields
    const nameValidation = validateName(formData.fullName);
    if (!nameValidation.isValid) {
      errors.fullName = nameValidation.error || '';
    }

    const companyValidation = validateCompany(formData.company);
    if (!companyValidation.isValid) {
      errors.company = companyValidation.error || '';
    }

    const emailValidation = validateEmail(formData.email);
    if (!emailValidation.isValid) {
      errors.email = emailValidation.error || '';
    }

    const phoneValidation = validatePhone(formData.phone, selectedCountry.code);
    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.error || '';
    }

    if (!formData.function) {
      errors.function = 'Please select a function';
    }

    if (!industry) {
      errors.industry = 'Please select an industry';
    }

    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.error || '';
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    // If there are validation errors, show them and return
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    // Format phone number with selected country code
    let phoneNumber = '';
    if (formData.phone.trim()) {
      const cleanPhone = formData.phone.replace(/[\s\-\(\)]/g, '');
      phoneNumber = selectedCountry.code + cleanPhone;
    }

    const payload = {
      name: formData.fullName,
      email: formData.email,
      password: formData.password,
      phone_number: phoneNumber,
      company: formData.company,
      category: industry, // Add industry as category
      sector: sector,
      function_name: formData.function, // Add function name
      objective_names: objectives, // Add objectives array
    };

    // Add detailed logging of the payload
    console.log("Sending registration payload:", JSON.stringify(payload));

    try {
      console.log("Attempting to fetch register endpoint...");
      const res = await fetch(API_BASE_URL + '/register', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload),
        mode: 'cors'
      });

      console.log("Register API response status:", res.status);

      if (res.status === 405) {
        throw new Error("API endpoint does not support POST method. Please contact support.");
      } else if (!res.ok) {
        const errResp = await res.json().catch(() => ({}));
        console.error("Registration error response:", errResp);

        // Extract specific error message from API response
        let errorMessage = `Registration failed with status: ${res.status}`;

        // Log the full error response for debugging
        console.log("Full error response structure:", JSON.stringify(errResp, null, 2));

        if (errResp.detail) {
          // Handle detailed error messages (common for validation errors)
          if (typeof errResp.detail === 'string') {
            errorMessage = errResp.detail;
          } else if (Array.isArray(errResp.detail)) {
            // Handle array of validation errors
            errorMessage = errResp.detail.map((err: any) =>
              typeof err === 'string' ? err : (err.msg || err.message || 'Validation error')
            ).join('; ');
          } else if (errResp.detail.message) {
            errorMessage = errResp.detail.message;
          }
        } else if (errResp.message) {
          errorMessage = errResp.message;
        } else if (errResp.error) {
          errorMessage = errResp.error;
        }

        // Enhance error messages for common scenarios
        if (errorMessage.toLowerCase().includes('email') && errorMessage.toLowerCase().includes('exist')) {
          errorMessage = "An account with this email address already exists. Please use a different email or try logging in.";
        } else if (errorMessage.toLowerCase().includes('phone') && errorMessage.toLowerCase().includes('exist')) {
          errorMessage = "An account with this phone number already exists. Please use a different phone number or try logging in.";
        } else if (errorMessage.toLowerCase().includes('duplicate') || errorMessage.toLowerCase().includes('already')) {
          // Generic duplicate handling for other fields
          errorMessage = errorMessage + " Please check your information and try again.";
        }

        throw new Error(errorMessage);
      }

      const data = await res.json();
      console.log("Registration successful:", data);

      toast({
        title: "Verification email sent!",
        description: "Please check your inbox for the verification email. Follow the link to verify and log in.",
      });
      setShowConfirmation(true);

    } catch (err: any) {
      console.error("Signup error:", err);

      // Determine appropriate title based on error message
      let title = "Signup failed";
      if (err.message && err.message.toLowerCase().includes('email') && err.message.toLowerCase().includes('exist')) {
        title = "Email already registered";
      } else if (err.message && err.message.toLowerCase().includes('phone') && err.message.toLowerCase().includes('exist')) {
        title = "Phone number already registered";
      } else if (err.message && (err.message.toLowerCase().includes('duplicate') || err.message.toLowerCase().includes('already'))) {
        title = "Account already exists";
      }

      toast({
        title: title,
        description: err.message || "An error occurred during signup.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubSectorSelect = (selectedSector: string) => {
    setIndustry('Manufacturing');
    setSector(selectedSector);
    setShowIndustryDropdown(false);
    // Clear industry error when selection is made
    if (fieldErrors.industry) {
      setFieldErrors(prev => ({ ...prev, industry: '' }));
    }
  };

  return (
    <div className={`min-h-screen flex flex-col ${localTheme === 'dark' ? 'bg-recrea-dark text-white' : 'bg-white text-recrea-dark'}`}>
      <div className="absolute top-4 right-4 z-10">
        <SignupThemeToggle theme={localTheme} onToggle={toggleSignupTheme} />
      </div>
      
      <div className="flex flex-col items-center justify-center flex-1 w-full px-4 py-12">
        <div className="mb-8 animate-fade-in">
          <Logo theme={localTheme} />
        </div>
        <div className="w-full max-w-xl animate-fade-in" style={{ animationDelay: '100ms' }}>
          <div className="text-center mb-6">
            <p className="text-muted-foreground">Recreating a better tomorrow</p>
          </div>
          
          <div className="glass-card p-8 overflow-hidden relative">
            {showConfirmation ? (
              <div className="text-center space-y-4">
                <div className="flex justify-center mb-4">
                  <div className="rounded-full bg-recrea-turquoise/20 p-3">
                    <Check className="h-8 w-8 text-recrea-turquoise" />
                  </div>
                </div>
                <h2 className="text-xl font-semibold">Thank you!</h2>
                <p className="text-muted-foreground">
                  Your user ID and default password have been sent to your registered email. 
                  Please log in and update your password.
                </p>
                <Button
                  className="primary-button mt-4"
                  onClick={navigateToLogin}
                >
                  Proceed to Login
                </Button>
              </div>
            ) : (
              <>
                <h2 className="text-2xl font-semibold text-center mb-6">Sign Up</h2>
                
                <div className="absolute top-0 right-0 w-full h-full">
                  <div className="absolute top-0 right-0 w-96 h-96 bg-recrea-turquoise/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>
                  <div className="absolute bottom-0 left-0 w-96 h-96 bg-recrea-teal/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"></div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        type="text"
                        name="fullName"
                        placeholder="Full Name *"
                        value={formData.fullName}
                        onChange={handleChange}
                        className={`glass-input w-full ${fieldErrors.fullName ? 'border-red-500' : ''}`}
                        required
                      />
                      {fieldErrors.fullName && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.fullName}</p>
                      )}
                    </div>

                    <div>
                      <Input
                        type="text"
                        name="company"
                        placeholder="Company Name *"
                        value={formData.company}
                        onChange={handleChange}
                        className={`glass-input w-full ${fieldErrors.company ? 'border-red-500' : ''}`}
                        required
                      />
                      {fieldErrors.company && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.company}</p>
                      )}
                    </div>

                    <div>
                      <Input
                        type="email"
                        name="email"
                        placeholder="Official Email ID *"
                        value={formData.email}
                        onChange={handleChange}
                        className={`glass-input w-full ${fieldErrors.email ? 'border-red-500' : ''}`}
                        required
                      />
                      {fieldErrors.email && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.email}</p>
                      )}
                    </div>

                    <div>
                      <div className="relative">
                        <div className="flex">
                          <div className="relative">
                            <button
                              type="button"
                              className="h-12 px-4 bg-white/80 border border-recrea-mint border-r-0 rounded-lg rounded-r-none text-foreground flex items-center gap-2 cursor-pointer hover:bg-white/90 dark:bg-white/10 dark:text-white dark:border-recrea-card-border dark:hover:bg-white/20 dropdown-container"
                              onClick={() => setShowCountryDropdown(!showCountryDropdown)}
                            >
                              <span className="text-sm font-medium whitespace-nowrap">
                                {selectedCountry.code}
                              </span>
                              <ChevronDown size={14} className="text-muted-foreground" />
                            </button>

                            {showCountryDropdown && (
                              <div className="absolute z-20 top-full left-0 mt-1 w-48 bg-card border border-input rounded-md shadow-lg max-h-60 overflow-auto">
                                {countryCodes.map(country => (
                                  <div
                                    key={country.code}
                                    className="px-4 py-3 hover:bg-muted cursor-pointer text-foreground flex items-center gap-2"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setSelectedCountry(country);
                                      setShowCountryDropdown(false);
                                    }}
                                  >
                                    <span className="font-medium min-w-[3rem]">{country.code}</span>
                                    <span className="text-sm text-muted-foreground">{country.country}</span>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                          <Input
                            type="tel"
                            name="phone"
                            placeholder="Phone Number"
                            value={formData.phone}
                            onChange={handleChange}
                            className={`h-12 bg-white/80 border border-recrea-mint rounded-lg rounded-l-none px-4 text-foreground focus:border-recrea-turquoise focus:ring-1 focus:ring-recrea-turquoise dark:bg-white/10 dark:text-white dark:border-recrea-card-border border-l-0 flex-1 ${fieldErrors.phone ? 'border-red-500' : ''}`}
                          />
                        </div>
                      </div>
                      {fieldErrors.phone && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.phone}</p>
                      )}
                    </div>
                    
                    <div className="relative dropdown-container">
                      <div
                        className={`glass-input w-full flex justify-between items-center cursor-pointer ${fieldErrors.function ? 'border-red-500' : ''}`}
                        onClick={() => setShowFunctionDropdown(!showFunctionDropdown)}
                      >
                        <span className={formData.function ? 'text-foreground' : 'text-muted-foreground'}>
                          {formData.function || 'Select Function *'}
                        </span>
                        <ChevronDown size={16} />
                      </div>

                      {showFunctionDropdown && (
                        <div className="absolute z-20 mt-1 w-full bg-card border border-input rounded-md shadow-lg max-h-60 overflow-auto">
                          {functions.map(func => (
                            <div
                              key={func}
                              className="px-4 py-2 hover:bg-muted cursor-pointer text-foreground"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, function: func }));
                                setShowFunctionDropdown(false);
                                if (fieldErrors.function) {
                                  setFieldErrors(prev => ({ ...prev, function: '' }));
                                }
                              }}
                            >
                              {func}
                            </div>
                          ))}
                        </div>
                      )}
                      {fieldErrors.function && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.function}</p>
                      )}
                    </div>
                    
                    <div className="relative md:col-span-2">
                      <DropdownMenu open={showIndustryDropdown} onOpenChange={setShowIndustryDropdown}>
                        <DropdownMenuTrigger asChild>
                          <div className={`glass-input w-full flex justify-between items-center cursor-pointer ${fieldErrors.industry ? 'border-red-500' : ''}`}>
                            <span className={industry ? 'text-foreground' : 'text-muted-foreground'}>
                              {industry ? (
                                <>
                                  {industry}
                                  {sector ? ` - ${sector}` : ''}
                                </>
                              ) : (
                                'Select Industry *'
                              )}
                            </span>
                            <ChevronDown size={16} />
                          </div>
                        </DropdownMenuTrigger>
                        
                        <DropdownMenuContent className="w-full z-50 bg-card">
                          <DropdownMenuSub>
                            <DropdownMenuSubTrigger className="cursor-pointer">
                              <span>Manufacturing</span>
                            </DropdownMenuSubTrigger>
                            <DropdownMenuPortal>
                              <DropdownMenuSubContent className="bg-card z-50">
                                {manufacturingSectors.map(sectorItem => (
                                  <DropdownMenuItem 
                                    key={sectorItem} 
                                    onClick={() => {
                                      setIndustry('Manufacturing');
                                      handleSubSectorSelect(sectorItem);
                                    }}
                                  >
                                    {sectorItem}
                                  </DropdownMenuItem>
                                ))}
                              </DropdownMenuSubContent>
                            </DropdownMenuPortal>
                          </DropdownMenuSub>
                          
                          {industries.slice(1).map(ind => (
                            <DropdownMenuItem
                              key={ind}
                              className="opacity-50 cursor-not-allowed"
                            >
                              {ind}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                      {fieldErrors.industry && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.industry}</p>
                      )}
                    </div>
                    
                    <div>
                      <Input
                        type="password"
                        name="password"
                        placeholder="Password *"
                        value={formData.password}
                        onChange={handleChange}
                        className={`glass-input w-full ${fieldErrors.password ? 'border-red-500' : ''}`}
                        required
                      />
                      {fieldErrors.password && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.password}</p>
                      )}
                      {formData.password && !fieldErrors.password && (
                        <div className="text-xs mt-1 space-y-1">
                          <p className="text-muted-foreground">Password requirements:</p>
                          <div className="grid grid-cols-2 gap-1 text-xs">
                            <span className={/[A-Z]/.test(formData.password) ? 'text-green-500' : 'text-muted-foreground'}>
                              ✓ Uppercase letter
                            </span>
                            <span className={/[a-z]/.test(formData.password) ? 'text-green-500' : 'text-muted-foreground'}>
                              ✓ Lowercase letter
                            </span>
                            <span className={/\d/.test(formData.password) ? 'text-green-500' : 'text-muted-foreground'}>
                              ✓ Number
                            </span>
                            <span className={/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(formData.password) ? 'text-green-500' : 'text-muted-foreground'}>
                              ✓ Special character
                            </span>
                            <span className={formData.password.length >= 8 ? 'text-green-500' : 'text-muted-foreground'}>
                              ✓ 8+ characters
                            </span>
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <Input
                        type="password"
                        name="confirmPassword"
                        placeholder="Confirm Password *"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        className={`glass-input w-full ${fieldErrors.confirmPassword ? 'border-red-500' : ''}`}
                        required
                      />
                      {fieldErrors.confirmPassword && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.confirmPassword}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-foreground mb-3">Objectives for Reaching Out:</h3>
                    <div className="flex flex-wrap gap-2">
                      {['Net Zero', 'Carbon Price', 'Energy Transition', 'Decarbonization', 'Others'].map((objective) => (
                        <button
                          key={objective}
                          type="button"
                          onClick={() => handleObjectiveToggle(objective)}
                          className={`px-4 py-2 rounded-full border text-sm font-medium transition-all ${
                            objectives.includes(objective) 
                              ? 'bg-recrea-turquoise/20 border-recrea-turquoise text-foreground' 
                              : 'border-border text-muted-foreground hover:border-recrea-turquoise'
                          }`}
                        >
                          {objective}
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex justify-center mt-6">
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="primary-button px-8"
                    >
                      {isLoading ? "Submitting..." : "Submit & Get Credentials"}
                    </Button>
                  </div>
                  
                  <div className="text-center mt-4">
                    <p className="text-sm text-muted-foreground">
                      Already have an account?{" "}
                      <button
                        type="button"
                        className="text-recrea-turquoise hover:underline"
                        onClick={navigateToLogin}
                      >
                        Login
                      </button>
                    </p>
                  </div>
                </form>
              </>
            )}
          </div>
        </div>
      </div>
      
      <div className="absolute top-0 left-0 w-full h-full -z-10 pointer-events-none overflow-hidden">
        <div className="absolute w-full h-40 bg-gradient-to-t from-background/50 to-transparent bottom-0"></div>
      </div>
    </div>
  );
};

export default SignUp;
