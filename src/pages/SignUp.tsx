import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, Check, ChevronRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Logo from '@/components/Logo';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import ThemeToggle from '@/components/ThemeToggle';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal
} from '@/components/ui/dropdown-menu';
import { API_BASE_URL } from '@/utils/endPoints';

const industries = [
  "Manufacturing",
  "Supply Chain",
  "Automotive",
  "Banking & Finance",
  "Energy"
];

const manufacturingSectors = [
  "Cement",
  "Steel",
  "Oil & Gas",
  "Chlor-Alkali",
  "Textile",
  "Aluminium",
  "Fertilizer",
  "Power",
  "Pulp & Paper"
];

const functions = [
  "Sustainability",
  "Risk",
  "Finance",
  "Procurement",
  "Operations",
  "Strategy",
  "R&D",
  "Legal & Compliance"
];

const SignUp = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    company: '',
    email: '',
    phone: '',
    function: '',
    password: '',
  });
  const [industry, setIndustry] = useState('Manufacturing');
  const [sector, setSector] = useState(manufacturingSectors[0]);
  const [showIndustryDropdown, setShowIndustryDropdown] = useState(false);
  const [showFunctionDropdown, setShowFunctionDropdown] = useState(false);
  const [objectives, setObjectives] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (industry === 'Manufacturing' && (!sector || !manufacturingSectors.includes(sector))) {
      setSector(manufacturingSectors[0]);
    }
    if (industry !== 'Manufacturing') {
      setSector('');
    }
  }, [industry]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleObjectiveToggle = (objective: string) => {
    setObjectives(prev =>
      prev.includes(objective)
        ? prev.filter(o => o !== objective)
        : [...prev, objective]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      toast({
        title: "Invalid email",
        description: "Please enter a valid email address",
        variant: "destructive",
      });
      return;
    }
    
    const phoneRegex = /^[\d\-\+\(\) ]{8,16}$/;
    if (formData.phone && !phoneRegex.test(formData.phone.trim())) {
      toast({
        title: "Invalid phone number",
        description: "Please enter a valid phone number (8-16 digits, digits allowed, spaces, +, -, () )",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    let phoneNumber = formData.phone.trim();
    if (phoneNumber && !phoneNumber.startsWith("+91")) {
      phoneNumber = "+91" + phoneNumber.replace(/^0+/, "");
    }

    const payload = {
      name: formData.fullName,
      email: formData.email,
      password: formData.password,
      phone_number: phoneNumber,
      company: formData.company,
      category: industry, // Add industry as category
      sector: sector,
    };

    // Add detailed logging of the payload
    console.log("Sending registration payload:", JSON.stringify(payload));

    try {
      console.log("Attempting to fetch register endpoint...");
      const res = await fetch(API_BASE_URL+'/register', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload),
        mode: 'cors'
      });

      console.log("Register API response status:", res.status);

      if (res.status === 405) {
        throw new Error("API endpoint does not support POST method. Please contact support.");
      } else if (!res.ok) {
        const errResp = await res.json().catch(() => ({}));
        console.error("Registration error response:", errResp);
        throw new Error(errResp.message || `Registration failed with status: ${res.status}`);
      }

      const data = await res.json();
      console.log("Registration successful:", data);

      toast({
        title: "Verification email sent!",
        description: "Please check your inbox for the verification email. Follow the link to verify and log in.",
      });
      setShowConfirmation(true);

    } catch (err: any) {
      console.error("Signup error:", err);
      toast({
        title: "Signup failed",
        description: err.message || "An error occurred during signup.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleIndustrySelect = (ind: string) => {
    setIndustry(ind);
    setShowIndustryDropdown(false);
  };

  const handleSubSectorSelect = (selectedSector: string) => {
    setIndustry('Manufacturing');
    setSector(selectedSector);
    setShowIndustryDropdown(false);
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>
      
      <div className="flex flex-col items-center justify-center flex-1 w-full px-4 py-12">
        <div className="mb-8 animate-fade-in">
          <Logo />
        </div>
        <div className="w-full max-w-xl animate-fade-in" style={{ animationDelay: '100ms' }}>
          <div className="text-center mb-6">
            <p className="text-muted-foreground">Creating a Better Tomorrow with AI</p>
          </div>
          
          <div className="glass-card p-8 overflow-hidden relative">
            {showConfirmation ? (
              <div className="text-center space-y-4">
                <div className="flex justify-center mb-4">
                  <div className="rounded-full bg-recrea-turquoise/20 p-3">
                    <Check className="h-8 w-8 text-recrea-turquoise" />
                  </div>
                </div>
                <h2 className="text-xl font-semibold">Thank you!</h2>
                <p className="text-muted-foreground">
                  Your user ID and default password have been sent to your registered email. 
                  Please log in and update your password.
                </p>
                <Button 
                  className="primary-button mt-4"
                  onClick={() => navigate('/login')}
                >
                  Proceed to Login
                </Button>
              </div>
            ) : (
              <>
                <h2 className="text-2xl font-semibold text-center mb-6">Sign Up</h2>
                
                <div className="absolute top-0 right-0 w-full h-full">
                  <div className="absolute top-0 right-0 w-96 h-96 bg-recrea-turquoise/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>
                  <div className="absolute bottom-0 left-0 w-96 h-96 bg-recrea-teal/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"></div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Input
                        type="text"
                        name="fullName"
                        placeholder="Full Name *"
                        value={formData.fullName}
                        onChange={handleChange}
                        className="glass-input w-full"
                        required
                      />
                    </div>
                    
                    <div>
                      <Input
                        type="text"
                        name="company"
                        placeholder="Company Name *"
                        value={formData.company}
                        onChange={handleChange}
                        className="glass-input w-full"
                        required
                      />
                    </div>
                    
                    <div>
                      <Input
                        type="email"
                        name="email"
                        placeholder="Official Email ID *"
                        value={formData.email}
                        onChange={handleChange}
                        className="glass-input w-full"
                        required
                      />
                    </div>

                    <div>
                      <div className="flex w-full items-center">
                        <span className="inline-flex px-3 py-2 bg-muted border border-input border-r-0 rounded-l-md text-foreground text-sm">
                          +91
                        </span>
                        <Input
                          type="tel"
                          name="phone"
                          placeholder="Phone Number *"
                          value={formData.phone}
                          onChange={handleChange}
                          className="glass-input w-full rounded-l-none border-l-0"
                          required
                          style={{ borderTopLeftRadius: 0, borderBottomLeftRadius: 0 }}
                        />
                      </div>
                    </div>
                    
                    <div className="relative">
                      <div 
                        className={`glass-input w-full flex justify-between items-center cursor-pointer`}
                        onClick={() => setShowFunctionDropdown(!showFunctionDropdown)}
                      >
                        <span className={formData.function ? 'text-foreground' : 'text-muted-foreground'}>
                          {formData.function || 'Function *'}
                        </span>
                        <ChevronDown size={16} />
                      </div>
                      
                      {showFunctionDropdown && (
                        <div className="absolute z-20 mt-1 w-full bg-card border border-input rounded-md shadow-lg max-h-60 overflow-auto">
                          {functions.map(func => (
                            <div
                              key={func}
                              className="px-4 py-2 hover:bg-muted cursor-pointer text-foreground"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, function: func }));
                                setShowFunctionDropdown(false);
                              }}
                            >
                              {func}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="relative md:col-span-2">
                      <DropdownMenu open={showIndustryDropdown} onOpenChange={setShowIndustryDropdown}>
                        <DropdownMenuTrigger asChild>
                          <div className="glass-input w-full flex justify-between items-center cursor-pointer">
                            <span className={industry ? 'text-foreground' : 'text-muted-foreground'}>
                              {industry}
                              {sector ? ` - ${sector}` : ''}
                            </span>
                            <ChevronDown size={16} />
                          </div>
                        </DropdownMenuTrigger>
                        
                        <DropdownMenuContent className="w-full z-50 bg-card">
                          <DropdownMenuSub>
                            <DropdownMenuSubTrigger className="cursor-pointer">
                              <span>Manufacturing</span>
                            </DropdownMenuSubTrigger>
                            <DropdownMenuPortal>
                              <DropdownMenuSubContent className="bg-card z-50">
                                {manufacturingSectors.map(sectorItem => (
                                  <DropdownMenuItem 
                                    key={sectorItem} 
                                    onClick={() => {
                                      setIndustry('Manufacturing');
                                      handleSubSectorSelect(sectorItem);
                                    }}
                                  >
                                    {sectorItem}
                                  </DropdownMenuItem>
                                ))}
                              </DropdownMenuSubContent>
                            </DropdownMenuPortal>
                          </DropdownMenuSub>
                          
                          {industries.slice(1).map(ind => (
                            <DropdownMenuItem
                              key={ind}
                              className="opacity-50 cursor-not-allowed"
                            >
                              {ind}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    
                    <div>
                      <Input
                        type="password"
                        name="password"
                        placeholder="Password *"
                        value={formData.password}
                        onChange={handleChange}
                        className="glass-input w-full"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-foreground mb-3">Objectives for Reaching Out:</h3>
                    <div className="flex flex-wrap gap-2">
                      {['Net Zero', 'Carbon Price', 'Energy Transition', 'Decarbonization', 'Others'].map((objective) => (
                        <button
                          key={objective}
                          type="button"
                          onClick={() => handleObjectiveToggle(objective)}
                          className={`px-4 py-2 rounded-full border text-sm font-medium transition-all ${
                            objectives.includes(objective) 
                              ? 'bg-recrea-turquoise/20 border-recrea-turquoise text-foreground' 
                              : 'border-border text-muted-foreground hover:border-recrea-turquoise'
                          }`}
                        >
                          {objective}
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex justify-center mt-4">
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="primary-button px-8"
                    >
                      {isLoading ? "Submitting..." : "Submit & Get Credentials"}
                    </Button>
                  </div>
                  
                  <div className="text-center mt-4">
                    <p className="text-sm text-muted-foreground">
                      Already have an account?{" "}
                      <button 
                        type="button" 
                        className="text-recrea-turquoise hover:underline"
                        onClick={() => navigate('/login')}
                      >
                        Login
                      </button>
                    </p>
                  </div>
                </form>
              </>
            )}
          </div>
        </div>
      </div>
      
      <div className="absolute top-0 left-0 w-full h-full -z-10 pointer-events-none overflow-hidden">
        <div className="absolute w-full h-40 bg-gradient-to-t from-background/50 to-transparent bottom-0"></div>
      </div>
    </div>
  );
};

export default SignUp;
