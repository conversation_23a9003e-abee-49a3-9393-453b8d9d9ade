import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { API_BASE_URL } from '@/utils/endPoints';

export interface MaterialTopic {
  id?: number;
  title: string;
  description: string;
  relevance: string;
  icon?: React.ReactNode;
}

interface FetchMaterialTopicsParams {
  sector: string;
  enabled?: boolean;
}

// Create a function to fetch material topics data that can be used outside the hook
export const fetchMaterialTopicsData = async (sector: string): Promise<MaterialTopic[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }
    
    const response = await fetch(API_BASE_URL +'/ask', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: "Dummy question",
        search_type: "local",
        technology_sector: sector,
        question_type: "Material Topics"
      })
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch material topics: ${response.status}`);
    }
    
    const data = await response.json();
    const materialTopicsData = data.response?.material_topics || [];
    
    return Array.isArray(materialTopicsData) && materialTopicsData.length > 0 ? materialTopicsData : [];
  } catch (err: any) {
    console.error('Error fetching material topics:', err);
    throw err;
  }
};

// Keep the existing hook for backward compatibility
export const useMaterialTopics = ({ sector, enabled = true }: FetchMaterialTopicsParams) => {
  const [materialTopics, setMaterialTopics] = useState<MaterialTopic[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      if (!sector || !enabled) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const data = await fetchMaterialTopicsData(sector);
        setMaterialTopics(data);
      } catch (err: any) {
        console.error('Error fetching material topics in hook:', err);
        setError(err.message || 'Failed to fetch material topics');
        toast({
          title: 'Error fetching material topics',
          description: err.message || 'Please try again later',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [sector, enabled, toast]);
  
  return { materialTopics, isLoading, error };
};
